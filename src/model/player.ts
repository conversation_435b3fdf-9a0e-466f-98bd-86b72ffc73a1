import { PlayerMatchHistory } from '@/entities/PlayerMatchHistory.js';

export interface Attribute {
  current: number;
  potential: number;
}

export interface Attributes {
  // goalkeeper
  reflexes: Attribute;
  positioning: Attribute;
  shotStopping: Attribute;
  // defender
  tackling: Attribute;
  marking: Attribute;
  heading: Attribute;
  // attacker
  finishing: Attribute;
  pace: Attribute;
  crossing: Attribute;
  // midfielder
  passing: Attribute;
  vision: Attribute;
  ballControl: Attribute;
}

export type PlayerMatchStats = Omit<PlayerMatchHistory, 'player' | 'fixtureId' | 'fixture'>;

// Interface for the current attributes without potential values
export interface CurrentAttributes {
  reflexes: number;
  positioning: number;
  shotStopping: number;
  tackling: number;
  marking: number;
  heading: number;
  finishing: number;
  pace: number;
  crossing: number;
  passing: number;
  vision: number;
  ballControl: number;
  stamina: number;
}

import {
  Manager,
  NotificationCategory,
  NotificationChannel,
  NotificationPreferences,
} from '@/entities/Manager.js';
import { Player } from '@/entities/Player.ts';
import { PlayerAttributes } from '@/entities/PlayerAttributes.js';
import { Team } from '@/entities/Team.js';
import { TransferRequest } from '@/entities/TransferRequest.js';
import { Repositories } from '@/middleware/database/types.js';
import { Auction } from '@/storage-interface/transfers/index.js';
import { EmailEvent } from '@/types/generated/email-event.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import { Expo, ExpoPushMessage } from 'expo-server-sdk';
import { SQS } from '../sqs/sqs.ts';

type Notification = {
  subject: string;
  content: string;
  title: string;
  category: NotificationCategory;
};

const sqs = new SQS({ tracer });

export class NotificationManager {
  private notificationPreferences: NotificationPreferences | undefined;
  private email: string | undefined;
  private pushToken: string | undefined;
  private currentManager: Manager | undefined;
  private repositories: Repositories | undefined;
  private static instance: NotificationManager;

  private constructor() {}

  static getInstance() {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }

  public async loadManagerPreferences(managerId: string, repositories: Repositories) {
    const manager = await repositories.managerRepository.getManagerById(managerId);
    if (!manager) {
      throw new Error(`Manager with ID ${managerId} not found`);
    }
    this.notificationPreferences = manager.notificationPreferences;
    this.email = manager.email;
    this.pushToken = manager.pushToken;
    this.currentManager = manager;
    this.repositories = repositories;
  }

  public assignManagerPreferences(manager: Manager, repositories?: Repositories) {
    this.notificationPreferences = manager.notificationPreferences;
    this.email = manager.email;
    this.pushToken = manager.pushToken;
    this.currentManager = manager;
    if (repositories) {
      this.repositories = repositories;
    }
  }

  private shouldSendNotification(
    prefs: NotificationPreferences,
    category: NotificationCategory,
    channel: NotificationChannel
  ): boolean {
    return Boolean(prefs?.[category]?.[channel]);
  }

  private async storeInInbox(notification: Notification) {
    try {
      // Only store in inbox if we have the repositories and manager context
      if (!this.repositories?.inboxRepository || !this.currentManager) {
        logger.debug('Skipping inbox storage - missing repository or manager context');
        return;
      }

      // Get team information from manager
      const gameworldId = this.currentManager.gameworldId;
      const teamId = this.currentManager.team?.teamId;

      if (!gameworldId || !teamId) {
        logger.warn('Cannot store in inbox - missing gameworldId or teamId', {
          managerId: this.currentManager.managerId,
          gameworldId,
          teamId,
        });
        return;
      }

      // Create inbox message with notification data
      const extra = JSON.stringify({
        category: notification.category,
        title: notification.title,
      });

      await this.repositories.inboxRepository.createMessage(
        gameworldId,
        teamId,
        Date.now(),
        notification.content,
        extra
      );

      logger.debug('Notification stored in inbox', {
        managerId: this.currentManager.managerId,
        gameworldId,
        teamId,
        category: notification.category,
      });
    } catch (error) {
      logger.error('Failed to store notification in inbox', {
        error,
        notification,
        managerId: this.currentManager?.managerId,
      });
      // Don't throw - inbox storage failure shouldn't break notification sending
    }
  }

  private async sendEmailNotification(notification: Notification) {
    try {
      const emailEvent: EmailEvent = {
        recipients: [this.email!],
        subject: notification.subject,
        content: notification.content,
        title: notification.title,
      };

      await sqs.send(process.env.EMAIL_QUEUE_URL!, JSON.stringify(emailEvent));
    } catch (error) {
      logger.error('Failed to send email to queue', {
        error,
        notification,
      });
    }
  }

  private async sendPushNotification(notification: Notification) {
    try {
      // Check if we have a valid push token
      if (!this.pushToken) {
        logger.warn('No push token available for notification', { notification });
        return;
      }

      // Validate the push token
      if (!Expo.isExpoPushToken(this.pushToken)) {
        logger.error('Invalid Expo push token', { pushToken: this.pushToken });
        return;
      }

      // Create a new Expo SDK client
      const expo = new Expo();

      // Construct the message
      const message: ExpoPushMessage = {
        to: this.pushToken,
        sound: 'default',
        title: notification.title,
        body: notification.content,
        data: {
          category: notification.category,
          content: notification.content,
        },
        // Set priority to high to ensure timely delivery
        priority: 'high',
      };

      try {
        // Send the push notification
        const ticketChunk = await expo.sendPushNotificationsAsync([message]);
        const ticket = ticketChunk[0];

        if (!ticket) {
          logger.error('Failed to send push notification', {
            error: 'No ticket returned',
            notification,
          });
          return;
        }
        if (ticket.status === 'error') {
          logger.error('Error sending push notification', {
            error: ticket.message,
            details: ticket.details,
            notification,
          });

          // Handle specific error cases
          if (ticket.details && ticket.details.error === 'DeviceNotRegistered') {
            logger.warn('Device is no longer registered for push notifications', {
              pushToken: this.pushToken,
            });
            // In a real implementation, you might want to update the database to clear this token
          }
        } else {
          logger.info('Push notification sent successfully', {
            ticketId: ticket.id,
            category: notification.category,
          });
        }
      } catch (error) {
        logger.error('Failed to send push notification', {
          error,
          notification,
        });
      }
    } catch (error) {
      logger.error('Error in sendPushNotification', {
        error,
        notification,
      });
    }
  }

  private async sendNotification(notification: Notification) {
    // Always store in inbox first (regardless of notification preferences)
    await this.storeInInbox(notification);

    // Don't send notifications if we don't have preferences
    if (!this.notificationPreferences) {
      return;
    }

    const promises = [];

    if (
      this.email &&
      this.shouldSendNotification(
        this.notificationPreferences,
        notification.category,
        NotificationChannel.EMAIL
      )
    ) {
      promises.push(this.sendEmailNotification(notification));
    }
    if (
      this.pushToken &&
      this.shouldSendNotification(
        this.notificationPreferences,
        notification.category,
        NotificationChannel.PUSH
      )
    ) {
      promises.push(this.sendPushNotification(notification));
    }

    return Promise.all(promises);
  }

  public auctionFailed(auction: Auction) {
    const playerName = auction.player.firstName + ' ' + auction.player.surname;
    const notification: Notification = {
      subject: `Auction ended without bids for ${playerName}`,
      content: `<p>Your player ${playerName} was listed for transfer but received no bids after 3 attempts.</p>
              <p>The player has been removed from the transfer list and remains in your team.</p>`,
      title: 'Transfer Auction Failed',
      category: NotificationCategory.TRANSFERS,
    };
    return this.sendNotification(notification);
  }

  public auctionWon(winningTeam: Team, playerName: string) {
    const notification: Notification = {
      subject: `You won the auction for ${playerName}!`,
      content: `<p>Congratulations, you were the highest bidder for ${playerName} and the player has now been transfered to ${winningTeam.teamName}.</p>
              <p>The player has expressed their joy at joining ${winningTeam.teamName}, a club they have supported since you agreed to pay them to kick a ball around.</p>`,
      title: 'Fresh Meat',
      category: NotificationCategory.TRANSFERS,
    };
    return this.sendNotification(notification);
  }

  public playerSold(winningTeam: Team, playerName: string, receivedFee: number) {
    const notification: Notification = {
      subject: `Your player ${playerName} has been sold!`,
      content: `<p>Your player ${playerName} has been sold to ${winningTeam.teamName} for a fee of £${receivedFee}. After agent fees, we will receive ${receivedFee * 0.8}.</p>
              <p>We wish them the best of luck in their new adventure and hope the door doesn't hit them on the way out.</p>`,
      title: 'Kerching!',
      category: NotificationCategory.TRANSFERS,
    };
    return this.sendNotification(notification);
  }

  public counterOfferMade(transferRequest: TransferRequest, counterOfferValue: number) {
    const notification: Notification = {
      subject: `Counter offer made for ${transferRequest.player.firstName} ${transferRequest.player.surname}`,
      content: `<p>A counter-offer has been made for ${transferRequest.player.firstName} ${transferRequest.player.surname}.</p>
              <p>The counter-offer is for £${counterOfferValue}.</p>`,
      title: 'Counter Offer Made',
      category: NotificationCategory.TRANSFERS,
    };
    return this.sendNotification(notification);
  }

  public transferRequestWithdrawn(transferRequest: TransferRequest, buyerTeamName: string) {
    const notification: Notification = {
      subject: `Transfer request withdrawn for ${transferRequest.player.firstName} ${transferRequest.player.surname}`,
      content: `<p>The transfer request from ${buyerTeamName} for your player ${transferRequest.player.firstName} ${transferRequest.player.surname} has been withdrawn.</p>
              <p>The buying team has decided not to proceed with the transfer at this time.</p>`,
      title: 'Transfer Request Withdrawn',
      category: NotificationCategory.TRANSFERS,
    };
    return this.sendNotification(notification);
  }

  public sendPlayerRetirementNotification(players: Player[]) {
    if (!players || players.length === 0) {
      logger.warn('No players to send retirement notifications for');
      return;
    }
    if (players.length === 1) {
      const playerName = `${players[0]!.firstName} ${players[0]!.surname}`;
      const age = players[0]!.age;
      const notification: Notification = {
        subject: `${players[0]!.surname} announces retirement`,
        content: `<p>Your player ${playerName} (age ${age}) has announced they will be retiring from the beautiful game at the end of the season. Apparently, their knees have filed for divorce and the local pub quiz team is offering a better contract.</p>
<p>They’ll see out the season before swapping shin pads for slippers and a pint. Please join us in raising a glass to their legendary service (and questionable fitness).</p>
<p>We wish them all the best in their new career as a full-time barstool pundit.</p>`,
        title: 'Player Retirement',
        category: NotificationCategory.TRANSFERS, // Using TRANSFERS as it's player-related
      };
      return this.sendNotification(notification);
    } else {
      const playerNames = players.map((p) => `${p.firstName} ${p.surname}`).join(', ');
      const ages = players.map((p) => p.age).join(', ');
      const notification: Notification = {
        subject: `Players announce retirement`,
        content: `<p>The following legends have decided to retire from pub football: ${playerNames} (ages: ${ages}).</p>
<p>They’ll finish the season before trading muddy boots for comfy slippers and a permanent spot at the bar.</p>
<p>Let’s raise a pint to their years of service, questionable tackles, and even more questionable fitness. May their next season be filled with pub quizzes and fewer pulled hamstrings!</p>`,
        title: 'Player Retirements',
        category: NotificationCategory.TRANSFERS, // Using TRANSFERS as it's player-related
      };
      return this.sendNotification(notification);
    }
  }

  sendTrainingCompleteNotification(playerName: string, attribute: string) {
    const notification: Notification = {
      subject: `Training complete for ${playerName}`,
      content: `<p>Your player ${playerName} has reached their full potential for ${attribute} and is off to the pub.</p>
              <p>You should reassign a new player to training or train ${playerName} in a different attribute.</p>`,
      title: 'Training Complete',
      category: NotificationCategory.TRAINING,
    };
    return this.sendNotification(notification);
  }

  // Helper to sum all *Potential fields in PlayerAttributes
  private getTotalPotential(attributes: PlayerAttributes): number {
    return (
      attributes.reflexesPotential +
      attributes.positioningPotential +
      attributes.shotStoppingPotential +
      attributes.tacklingPotential +
      attributes.markingPotential +
      attributes.headingPotential +
      attributes.finishingPotential +
      attributes.pacePotential +
      attributes.crossingPotential +
      attributes.passingPotential +
      attributes.visionPotential +
      attributes.ballControlPotential
    );
  }

  // Returns the player with the highest total potential
  private getPlayerWithHighestPotential(players: Player[]): Player | undefined {
    if (!players.length) return undefined;
    return players.reduce(
      (best, player) => {
        if (!player.attributes) return best;
        const playerPotential = this.getTotalPotential(player.attributes as PlayerAttributes);
        const bestPotential =
          best && best.attributes
            ? this.getTotalPotential(best.attributes as PlayerAttributes)
            : -Infinity;
        return playerPotential > bestPotential ? player : best;
      },
      undefined as Player | undefined
    );
  }

  public async sendYouthPlayerNotification(allPlayers: Player[]) {
    const bestPlayer = this.getPlayerWithHighestPotential(allPlayers);
    const notification: Notification = {
      subject: `Kids. Who'd have 'em?`,
      content: `<p>Three new yoofs have joined the team. ${allPlayers.map((p) => `${p.firstName} ${p.surname}`).join(', ')}.</p>
              <p>In particular, ${bestPlayer?.firstName} ${bestPlayer?.surname} has shown great promise.</p>`,
      title: 'New Youth Players',
      category: NotificationCategory.TRANSFERS,
    };
    return this.sendNotification(notification);
  }
}

import { EventWithRepositories } from '@/middleware/event/types.ts';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { NotificationManager } from '@/services/notifications/NotificationManager.js';
import createSqsEvent from '@/testing/createSqsEvent.js';
import { mockPlayerRepository } from '@/testing/mockRepositories.js';
import { TrainingImprovementEvent } from '@/types/generated/training-improvement-event.ts';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './processTrainingImprovements.js';

describe('processTrainingImprovements handler', () => {
  const mockContext = {} as any;
  let mockEvent: SQSEvent<TrainingImprovementEvent> & EventWithRepositories;

  const loadManagerPreferencesSpy = vi
    .spyOn(NotificationManager.prototype, 'loadManagerPreferences')
    .mockResolvedValue(undefined);
  const sendTrainingCompleteNotificationSpy = vi
    .spyOn(NotificationManager.prototype, 'sendTrainingCompleteNotification')
    .mockResolvedValue(undefined);

  beforeEach(() => {
    vi.clearAllMocks();
    mockEvent = createSqsEvent([
      {
        body: {
          playerId: 'player-1',
          attribute: 'finishing',
          current: 50,
          potential: 100,
          trainingMultiplier: 10,
        },
      },
      {
        body: {
          playerId: 'player-2',
          attribute: 'passing',
          current: 30,
          potential: 100,
          trainingMultiplier: 5,
        },
      },
    ]) as any;
  });

  it('updates player attributes for valid training improvement events', async () => {
    await handler(mockEvent, mockContext);

    expect(mockPlayerRepository.updatePlayerAttributesBatch).toHaveBeenCalledWith([
      { playerId: 'player-1', attribute: 'finishingCurrent', attributeIncrement: 5.5 },
      { playerId: 'player-2', attribute: 'passingCurrent', attributeIncrement: 3.65 },
    ]);
    expect(loadManagerPreferencesSpy).not.toHaveBeenCalled();
    expect(sendTrainingCompleteNotificationSpy).not.toHaveBeenCalled();
  });

  it('handles edge case where current equals potential', async () => {
    mockEvent = createSqsEvent([
      {
        body: {
          playerId: 'player-1',
          attribute: 'finishing',
          current: 100,
          potential: 100,
          trainingMultiplier: 10,
          managerId: 'manager-1',
          playerName: 'Player One',
        },
      },
    ]) as any;

    await handler(mockEvent, mockContext);

    expect(mockPlayerRepository.updatePlayerAttributesBatch).not.toHaveBeenCalled();
    expect(loadManagerPreferencesSpy).toHaveBeenCalledWith('manager-1', expect.anything());
    expect(sendTrainingCompleteNotificationSpy).toHaveBeenCalledWith('Player One', 'finishing');
  });
});

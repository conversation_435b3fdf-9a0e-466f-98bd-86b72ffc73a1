import { SQS } from '@/services/sqs/sqs.js';
import { mockTrainingRepository } from '@/testing/mockRepositories.js';
import { SendMessageBatchRequestEntry, SendMessageCommandInput } from '@aws-sdk/client-sqs';
import { beforeEach, describe, expect, it, MockInstance, vi } from 'vitest';
import { handler } from './scheduleTrainingImprovement.js';

describe('scheduleTrainingImprovement handler', () => {
  const mockContext = {} as any;
  const mockEvent = {} as any;
  let sendBatchSpy: MockInstance<
    (
      queueUrl: string,
      entries: SendMessageBatchRequestEntry[],
      additionalOpts?: Partial<SendMessageCommandInput>
    ) => Promise<void>
  >;
  beforeEach(() => {
    vi.clearAllMocks();
    mockTrainingRepository.getAllFilledSlots.mockResolvedValue([]);
    sendBatchSpy = vi.spyOn(SQS.prototype, 'sendBatch').mockResolvedValue({} as any);
    process.env.TRAINING_QUEUE_URL = 'test-queue-url';
  });

  it('sends training improvement events for all filled slots', async () => {
    mockTrainingRepository.getAllFilledSlots.mockResolvedValue([
      {
        id: 'slot-1',
        player: {
          playerId: 'player-1',
          firstName: 'Player',
          surname: 'One',
          attributes: {
            finishingCurrent: 50,
            finishingPotential: 80,
          },
        },
        team: {
          trainingLevel: 5,
        },
        attribute: 'finishing',
      },
      {
        id: 'slot-2',
        player: {
          playerId: 'player-2',
          firstName: 'Player',
          surname: 'Two',
          attributes: {
            passingCurrent: 60,
            passingPotential: 90,
          },
        },
        team: {
          trainingLevel: 5,
        },
        attribute: 'passing',
      },
    ]);

    await handler(mockEvent, mockContext);

    expect(sendBatchSpy).toHaveBeenCalledWith(process.env.TRAINING_QUEUE_URL!, [
      {
        Id: 'slot-1',
        MessageBody:
          '{"playerId":"player-1","playerName":"Player One","attribute":"finishing","current":50,"potential":80,"trainingMultiplier":0.5}',
      },
      {
        Id: 'slot-2',
        MessageBody:
          '{"playerId":"player-2","playerName":"Player Two","attribute":"passing","current":60,"potential":90,"trainingMultiplier":0.5}',
      },
    ]);
  });

  it('does not send any events if no filled slots are found', async () => {
    mockTrainingRepository.getAllFilledSlots.mockResolvedValue([]);

    await handler(mockEvent, mockContext);

    expect(sendBatchSpy).not.toHaveBeenCalled();
  });

  it('throws an error if SQS send fails', async () => {
    mockTrainingRepository.getAllFilledSlots.mockResolvedValue([
      {
        player: {
          playerId: 'player-1',
          attributes: {
            finishingCurrent: 50,
            finishingPotential: 80,
          },
        },
        team: {
          trainingLevel: 5,
        },
        attribute: 'finishing',
      },
    ]);
    sendBatchSpy.mockRejectedValue(new Error('SQS error'));

    await expect(handler(mockEvent, mockContext)).rejects.toThrow('SQS error');
  });

  it('throws an error if filled slots retrieval fails', async () => {
    mockTrainingRepository.getAllFilledSlots.mockRejectedValue(new Error('Database error'));

    await expect(handler(mockEvent, mockContext)).rejects.toThrow('Database error');
  });
});

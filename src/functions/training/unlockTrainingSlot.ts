import { TeamTrainingSlot } from '@/entities/TeamTrainingSlot.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { unlockTrainingSlotSchema } from './schema.js';

interface Body {
  slotIndex: number;
}

const unlockCosts = [0, 1000000, 3000000, 7000000, 15000000];

const main = async (event: HttpEvent<Body, void, void>) => {
  const { trainingRepository, teamRepository, managerRepository } = event.context.repositories;
  const { slotIndex } = event.body;

  const userId = getUser(event);
  const manager = await managerRepository.getManagerById(userId);
  if (!manager || !manager.team) {
    return buildResponse(404, JSON.stringify({ error: 'Manager or team not found' }));
  }
  const team = manager.team;

  // Check if slot exists
  const slots = await trainingRepository.getSlotsByTeam(team.teamId);
  let slot = slots.find((s: TeamTrainingSlot) => s.slotIndex === slotIndex);

  // If slot exists and is unlocked (i.e., present in DB), return early
  if (slot) {
    return buildResponse(200, JSON.stringify({ success: true, alreadyUnlocked: true }));
  }

  // Check team balance
  const cost = unlockCosts[slotIndex]!;
  if (team.balance < cost) {
    return buildResponse(400, JSON.stringify({ error: 'Insufficient funds' }));
  }

  // Deduct funds and create slot
  await teamRepository.updateTeamBalance(team.teamId, team.gameworldId, -cost);

  slot = new TeamTrainingSlot();
  slot.team = teamRepository.createFromPK(team.teamId);
  slot.slotIndex = slotIndex;
  await trainingRepository.createSlot(slot);

  return buildResponse(200, JSON.stringify({ success: true, balance: team.balance - cost }));
};

export const handler = httpMiddify(main, { schema: unlockTrainingSlotSchema });

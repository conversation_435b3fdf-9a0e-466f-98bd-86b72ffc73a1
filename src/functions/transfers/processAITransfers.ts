import { Team } from '@/entities/Team.js';
import { Repositories } from '@/middleware/database/types.js';
import { <PERSON><PERSON>andler } from '@/middleware/event/types.js';
import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { DBTransferListedPlayer } from '@/storage-interface/transfers/index.js';
import { SimulateAiTransfers } from '@/types/generated/simulate-ai-transfers.js';
import { logger } from '@/utils/logger.js';
import { seededRandomIntInRange, setAndReturnSeededRandom } from '@/utils/seeded-random.js';

function processMessage(
  team: Team,
  transferListedPlayers: DBTransferListedPlayer[],
  repositories: Repositories
) {
  if (team.players.length >= 25) {
    logger.debug(`Team ${team.teamName} has 25 players. Not bidding on any more players`);
    // already too many players to get more. Make sure we have at least three players
    // in the transfer list. If not, add three random players to the transfer list
    const playersListed = team.players.filter((p) => p.isTransferListed).length;
    if (playersListed < 3) {
      const playersToAdd = 3 - playersListed;
      const players = team.players.filter((p) => !p.isTransferListed);
      const randomPlayers = players
        .sort(() => Math.random() - Math.random())
        .slice(0, playersToAdd);
      logger.debug(
        `Team ${team.teamName} is adding ${randomPlayers.length} players to the transfer list`,
        { randomPlayers }
      );
      const promises = randomPlayers.map((player) =>
        repositories.transferRepository.addTransferListedPlayer(player)
      );
      return Promise.all(promises);
    }
    // nothing else to do
    return Promise.resolve();
  }
  // if we have less than 25 players, we should bid on a random player
  // 9 times out of 10, just return without doing anything
  if (seededRandomIntInRange(1, 15) === 1) {
    // filter transferListedPlayers to only include players that are not already on this team
    // and also whose current auction price is less than the team's balance
    const filteredTransferListedPlayers = transferListedPlayers.filter(
      (player) =>
        player.player.team?.teamId !== team.teamId && player.auctionCurrentPrice < team.balance
    );
    if (filteredTransferListedPlayers.length === 0) {
      logger.debug(`No suitable transfer listed players found`);
      return Promise.resolve();
    }
    // get a random player from the transfer listed players
    const randomPlayer =
      filteredTransferListedPlayers[
        seededRandomIntInRange(0, filteredTransferListedPlayers.length - 1)
      ];
    if (randomPlayer) {
      // we will bid up to 10% on top of the current bid price of the player or our balance, whichever is lower
      const maxBid = Math.min(randomPlayer.auctionCurrentPrice * 1.1, team.balance);
      logger.debug(
        `Team ${team.teamName} is bidding ${maxBid} on player ${randomPlayer.player.playerId}`
      );
      return repositories.transferRepository.submitBid(
        randomPlayer.player.playerId,
        maxBid,
        team.teamId
      );
    } else {
      logger.debug(`No transfer listed players found`);
      return Promise.resolve();
    }
  } else {
    logger.debug(`Team ${team.teamName} is not bidding on a player`);
    return Promise.resolve();
  }
}

/**
 * Lambda handler that processes SQS events containing team IDs for AI transfers
 */
const main: EventHandler<SQSEvent<SimulateAiTransfers>> = async (event) => {
  logger.debug(`Processing ${event.Records.length} AI transfer messages`);
  setAndReturnSeededRandom();

  // Group records by gameworldId
  const recordsByGameworld = event.Records.reduce(
    (acc, record) => {
      const { gameworldId } = record.body;
      if (!acc[gameworldId]) {
        acc[gameworldId] = [];
      }
      acc[gameworldId].push(record);
      return acc;
    },
    {} as Record<string, typeof event.Records>
  );

  const { teamRepository, transferRepository } = event.context.repositories;

  // Process each gameworld batch separately
  const gameworldPromises = Object.entries(recordsByGameworld).map(
    async ([gameworldId, records]) => {
      // Get team IDs for this gameworld
      const teamIds = records.map((record) => record.body.teamId);

      // Fetch AI teams for this gameworld
      const aiTeams = await teamRepository.findByIds(teamIds, true);
      logger.debug(`Found ${aiTeams.length} AI teams for gameworld ${gameworldId}`);

      // Fetch transfer listed players for this gameworld
      const transferListedPlayers = await transferRepository.getTransferListedPlayers(
        gameworldId,
        100
      );

      logger.debug(
        `Found ${transferListedPlayers.length} transfer listed players for gameworld ${gameworldId}`
      );

      // Process each team in this gameworld
      const teamPromises = aiTeams.map((team) => {
        return processMessage(team, transferListedPlayers, event.context.repositories);
      });

      // Wait for all teams in this gameworld to be processed
      return Promise.all(teamPromises);
    }
  );

  // Wait for all gameworlds to be processed
  await Promise.all(gameworldPromises);

  logger.debug('Completed processing AI transfer batch');
};

export const handler = sqsMiddify<SimulateAiTransfers>(main);

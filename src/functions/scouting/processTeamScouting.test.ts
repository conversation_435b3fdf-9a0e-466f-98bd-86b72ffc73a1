import { Player } from '@/entities/Player.js';
import { ScoutingRequest } from '@/model/scouting.js';
import createSqsEvent from '@/testing/createSqsEvent.js';
import { PlayerFactory } from '@/testing/factories/playerFactory.js';
import { mockScoutingRepository } from '@/testing/mockRepositories.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './processTeamScouting.js';

describe('Process Team Scouting Lambda', () => {
  const mockContext = {} as any;

  const mockGameworldId = 'test-gameworld-id';
  const mockTeamId = 'test-team-id';

  const mockScoutingRequest: ScoutingRequest = {
    type: 'team',
    id: mockTeamId, // id is the teamId in this case
    teamId: mockTeamId,
    gameworldId: mockGameworldId,
    processAfter: Date.now(),
    pk: `${mockGameworldId}#${mockTeamId}`,
    requestId: 'request-1',
  };

  // Create mock players
  const createMockPlayer = (id: number): Player =>
    PlayerFactory.build({
      playerId: `player-${id}`,
      gameworldId: mockGameworldId,
      firstName: `First${id}`,
      surname: `Last${id}`,
      age: 25,
      seed: 12345,
      value: 100000,
      energy: 100,
    });

  const mockPlayers: Player[] = Array.from({ length: 10 }, (_, i) => createMockPlayer(i + 1));

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should successfully process a team scouting request with no existing scouted players', async () => {
    // Create SQS event with a scouting request
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Set the parsed body directly (middleware would normally parse this)
    event.Records[0].body = mockScoutingRequest;

    // Add repository context that middleware would normally provide
    mockScoutingRepository.scoutPlayersFromTeam.mockResolvedValue(mockPlayers.slice(0, 5));

    // Execute the handler
    await handler(event, mockContext);

    // Verify the repository was called with correct parameters
    expect(mockScoutingRepository.scoutPlayersFromTeam).toHaveBeenCalledWith(
      mockGameworldId,
      mockTeamId, // target team (team being scouted)
      mockTeamId, // scouting team (team doing the scouting)
      5
    );

    // Verify the repository was called once
    expect(mockScoutingRepository.scoutPlayersFromTeam).toHaveBeenCalledTimes(1);
  });

  it('should only scout players that have not been scouted before', async () => {
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Set the parsed body directly (middleware would normally parse this)
    event.Records[0].body = mockScoutingRequest;

    // Add repository context that returns only new players (repository handles filtering)
    const newPlayers = mockPlayers.slice(3, 8); // Return 5 new players (excluding first 3)
    mockScoutingRepository.scoutPlayersFromTeam.mockResolvedValue(newPlayers);

    // Execute the handler
    await handler(event, mockContext);

    // Verify the repository was called with correct parameters
    expect(mockScoutingRepository.scoutPlayersFromTeam).toHaveBeenCalledWith(
      mockGameworldId,
      mockTeamId, // target team (team being scouted)
      mockTeamId, // scouting team (team doing the scouting)
      5
    );

    // Verify the repository was called once
    expect(mockScoutingRepository.scoutPlayersFromTeam).toHaveBeenCalledTimes(1);
  });

  it('should not update scouted players if all players have already been scouted', async () => {
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Set the parsed body directly (middleware would normally parse this)
    event.Records[0].body = mockScoutingRequest;

    // Add repository context that returns no new players (all already scouted)
    mockScoutingRepository.scoutPlayersFromTeam.mockResolvedValue([]);

    // Execute the handler
    await handler(event, mockContext);

    // Verify the repository was called but returned no players
    expect(mockScoutingRepository.scoutPlayersFromTeam).toHaveBeenCalledWith(
      mockGameworldId,
      mockTeamId,
      mockTeamId,
      5
    );
    expect(mockScoutingRepository.scoutPlayersFromTeam).toHaveBeenCalledTimes(1);
  });

  it('should handle multiple scouting requests in batch', async () => {
    // Create multiple mock requests for the same team
    const secondRequest = {
      ...mockScoutingRequest,
      requestId: 'request-2',
    };

    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
      {
        body: secondRequest,
      },
    ]) as any;

    // Set the parsed bodies directly (middleware would normally parse this)
    event.Records[0].body = mockScoutingRequest;
    event.Records[1].body = secondRequest;

    // Add repository context
    mockScoutingRepository.scoutPlayersFromTeam.mockResolvedValue(mockPlayers.slice(0, 5));

    // Execute the handler
    await handler(event, mockContext);

    // Verify repository was called twice (once for each request)
    expect(mockScoutingRepository.scoutPlayersFromTeam).toHaveBeenCalledTimes(2);
  });

  it('should work normally even if environment variables are not set', async () => {
    delete process.env.PLAYERS_TABLE_NAME;
    delete process.env.SCOUTED_PLAYERS_TABLE_NAME;

    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Set the parsed body directly (middleware would normally parse this)
    event.Records[0].body = mockScoutingRequest;

    // Add repository context (function now uses repositories, not env vars directly)
    mockScoutingRepository.scoutPlayersFromTeam.mockResolvedValue(mockPlayers.slice(0, 5));

    // Execute the handler - should work normally with repositories
    await handler(event, mockContext);

    // Verify the repository was called
    expect(mockScoutingRepository.scoutPlayersFromTeam).toHaveBeenCalledTimes(1);
  });

  it('should handle database errors gracefully', async () => {
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Set the parsed body directly (middleware would normally parse this)
    event.Records[0].body = mockScoutingRequest;

    // Add repository context that throws an error
    const mockError = new Error('Database error');
    mockScoutingRepository.scoutPlayersFromTeam.mockRejectedValue(mockError);

    // Verify error is thrown to trigger SQS retry
    await expect(handler(event, mockContext)).rejects.toThrow('Database error');
  });

  it('should skip processing if no players are found for the team', async () => {
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Set the parsed body directly (middleware would normally parse this)
    event.Records[0].body = mockScoutingRequest;

    // Add repository context that returns no players
    mockScoutingRepository.scoutPlayersFromTeam.mockResolvedValue([]);

    // Execute the handler
    await handler(event, mockContext);

    // Verify the repository was called but returned no players
    expect(mockScoutingRepository.scoutPlayersFromTeam).toHaveBeenCalledTimes(1);
  });
});

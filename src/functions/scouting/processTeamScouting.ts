import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { ScoutingRequest } from '@/model/scouting.js';
import { logger } from '@/utils/logger.js';

async function main(event: SQSEvent<ScoutingRequest>) {
  // Get the scoutingRepository from the repositories
  const { scoutingRepository } = event.context.repositories;

  // Group requests by gameworld and target team (the team being scouted)
  const requestsByGameworldAndTargetTeam: { [key: string]: ScoutingRequest[] } = {};

  for (const record of event.Records) {
    const request = record.body;
    const key = `${request.gameworldId}#${request.id}`; // id is the target teamId (team being scouted)
    if (!requestsByGameworldAndTargetTeam[key]) {
      requestsByGameworldAndTargetTeam[key] = [];
    }
    requestsByGameworldAndTargetTeam[key].push(request);
  }

  // Process each group of requests
  for (const [key, requests] of Object.entries(requestsByGameworldAndTargetTeam)) {
    const [gameworldId, targetTeamId] = key.split('#');

    logger.debug('Processing team scouting requests', {
      gameworldId,
      targetTeamId,
      requestCount: requests.length,
    });

    try {
      // Process each request (from different scouting teams)
      for (const request of requests) {
        const scoutingTeamId = request.teamId; // The team doing the scouting

        // Get players from the target team that haven't been scouted yet by the scouting team
        // and mark them as scouted
        const playersPerRequest = 5;
        const scoutedPlayers = await scoutingRepository.scoutPlayersFromTeam(
          gameworldId!,
          targetTeamId!,
          scoutingTeamId,
          playersPerRequest
        );

        if (scoutedPlayers.length === 0) {
          logger.debug('No new players to scout for this team', {
            gameworldId,
            targetTeamId,
            scoutingTeamId,
          });
          continue;
        }

        logger.debug('Scouted players from team', {
          gameworldId,
          targetTeamId,
          scoutingTeamId,
          scoutedPlayerCount: scoutedPlayers.length,
          scoutedPlayerIds: scoutedPlayers.map((p) => p.playerId),
        });
      }
    } catch (error) {
      logger.error('Failed to process team scouting request', {
        error,
        gameworldId,
        targetTeamId,
      });
      throw error; // Let SQS retry
    }
  }
}

export const handler = sqsMiddify<ScoutingRequest>(main, {});

import { ScoutingRequest } from '@/model/scouting.js';
import createSqsEvent from '@/testing/createSqsEvent.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './processPlayerScouting.js';

// Mock SQS middleware before importing the handler
vi.mock('@/middleware/sqs/index.js', () => ({
  sqsMiddify: vi.fn((handler) => handler),
}));

vi.mock('@/services/database/dynamo/dynamo-db-service');
vi.mock('@/utils/logger');

describe('Process Player Scouting Lambda', () => {
  const mockContext = {} as any;

  const mockGameworldId = 'test-gameworld-id';
  const mockTeamId = 'test-team-id';
  const mockPlayerId = 'test-player-id';

  const mockScoutingRequest: ScoutingRequest = {
    type: 'player',
    id: mockPlayerId,
    teamId: mockTeamId,
    gameworldId: mockGameworldId,
    processAfter: Date.now(),
    pk: `${mockGameworldId}#${mockTeamId}`,
    requestId: 'request-1',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    process.env.SCOUTED_PLAYERS_TABLE_NAME = 'scouted-players-table';
  });

  it('should successfully process a player scouting request', async () => {
    // Create SQS event with a scouting request
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Set the parsed body directly (middleware would normally parse this)
    event.Records[0].body = mockScoutingRequest;

    // Add repository context that middleware would normally provide
    const mockScoutingRepository = {
      saveScoutedPlayers: vi.fn().mockResolvedValue(undefined),
    };

    event.context = {
      repositories: {
        scoutingRepository: mockScoutingRepository,
      },
    };

    // Execute the handler
    await handler(event, mockContext);

    // Verify the repository was called with correct parameters
    expect(mockScoutingRepository.saveScoutedPlayers).toHaveBeenCalledWith(
      mockGameworldId,
      mockTeamId,
      [mockPlayerId]
    );

    // Verify the repository was called once
    expect(mockScoutingRepository.saveScoutedPlayers).toHaveBeenCalledTimes(1);
  });

  it('should process player scouting request with repository', async () => {
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Set the parsed body directly (middleware would normally parse this)
    event.Records[0].body = mockScoutingRequest;

    // Add repository context that middleware would normally provide
    const mockScoutingRepository = {
      saveScoutedPlayers: vi.fn().mockResolvedValue(undefined),
    };

    event.context = {
      repositories: {
        scoutingRepository: mockScoutingRepository,
      },
    };

    // Execute the handler
    await handler(event, mockContext);

    // Verify the repository was called with correct parameters
    expect(mockScoutingRepository.saveScoutedPlayers).toHaveBeenCalledWith(
      mockGameworldId,
      mockTeamId,
      [mockPlayerId]
    );

    // Verify the repository was called once
    expect(mockScoutingRepository.saveScoutedPlayers).toHaveBeenCalledTimes(1);
  });

  it('should handle multiple scouting requests from different teams', async () => {
    // Create a request from a different team
    const secondTeamId = 'team-2';
    const secondRequest = {
      ...mockScoutingRequest,
      teamId: secondTeamId,
      requestId: 'request-2',
    };

    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
      {
        body: secondRequest,
      },
    ]) as any;

    // Set the parsed bodies directly (middleware would normally parse this)
    event.Records[0].body = mockScoutingRequest;
    event.Records[1].body = secondRequest;

    // Add repository context that middleware would normally provide
    const mockScoutingRepository = {
      saveScoutedPlayers: vi.fn().mockResolvedValue(undefined),
    };

    event.context = {
      repositories: {
        scoutingRepository: mockScoutingRepository,
      },
    };

    // Execute the handler
    await handler(event, mockContext);

    // Verify the repository was called twice (once for each team)
    expect(mockScoutingRepository.saveScoutedPlayers).toHaveBeenCalledTimes(2);

    // Verify first team's call
    expect(mockScoutingRepository.saveScoutedPlayers).toHaveBeenCalledWith(
      mockGameworldId,
      mockTeamId,
      [mockPlayerId]
    );

    // Verify second team's call
    expect(mockScoutingRepository.saveScoutedPlayers).toHaveBeenCalledWith(
      mockGameworldId,
      secondTeamId,
      [mockPlayerId]
    );
  });

  it('should handle multiple players in a single batch', async () => {
    // Create a request for a different player
    const secondPlayerId = 'player-2';
    const secondRequest = {
      ...mockScoutingRequest,
      id: secondPlayerId,
      requestId: 'request-2',
    };

    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
      {
        body: secondRequest,
      },
    ]) as any;

    // Set the parsed bodies directly (middleware would normally parse this)
    event.Records[0].body = mockScoutingRequest;
    event.Records[1].body = secondRequest;

    // Add repository context that middleware would normally provide
    const mockScoutingRepository = {
      saveScoutedPlayers: vi.fn().mockResolvedValue(undefined),
    };

    event.context = {
      repositories: {
        scoutingRepository: mockScoutingRepository,
      },
    };

    // Execute the handler
    await handler(event, mockContext);

    // Verify the repository was called once with both player IDs
    expect(mockScoutingRepository.saveScoutedPlayers).toHaveBeenCalledTimes(1);
    expect(mockScoutingRepository.saveScoutedPlayers).toHaveBeenCalledWith(
      mockGameworldId,
      mockTeamId,
      [mockPlayerId, secondPlayerId]
    );
  });

  it('should work normally even if environment variables are not set', async () => {
    delete process.env.SCOUTED_PLAYERS_TABLE_NAME;

    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Set the parsed body directly (middleware would normally parse this)
    event.Records[0].body = mockScoutingRequest;

    // Add repository context (function now uses repositories, not env vars directly)
    const mockScoutingRepository = {
      saveScoutedPlayers: vi.fn().mockResolvedValue(undefined),
    };

    event.context = {
      repositories: {
        scoutingRepository: mockScoutingRepository,
      },
    };

    // Execute the handler - should work normally with repositories
    await handler(event, mockContext);

    // Verify the repository was called
    expect(mockScoutingRepository.saveScoutedPlayers).toHaveBeenCalledTimes(1);
  });

  it('should handle database errors gracefully', async () => {
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Set the parsed body directly (middleware would normally parse this)
    event.Records[0].body = mockScoutingRequest;

    // Add repository context that throws an error
    const mockError = new Error('Database error');
    const mockScoutingRepository = {
      saveScoutedPlayers: vi.fn().mockRejectedValue(mockError),
    };

    event.context = {
      repositories: {
        scoutingRepository: mockScoutingRepository,
      },
    };

    // Verify error is thrown to trigger SQS retry
    await expect(handler(event, mockContext)).rejects.toThrow('Database error');
  });
});

import { Player } from '@/entities/Player.js';
import { ScoutingRequest } from '@/model/scouting.js';
import createSqsEvent from '@/testing/createSqsEvent.js';
import { PlayerFactory } from '@/testing/factories/playerFactory.js';
import { mockScoutingRepository } from '@/testing/mockRepositories.js';
import { seededRandom } from '@/utils/seeded-random.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './processLeagueScouting.js';

// Mock dependencies
vi.mock('@/utils/seeded-random');

describe('Process League Scouting', () => {
  const mockContext = {} as any;

  // Mock data
  const mockGameworldId = 'test-gameworld-id';
  const mockLeagueId = 'test-league-id';
  const mockTeamId = 'test-team-id';

  // Create mock players
  const createMockPlayer = (id: number): Player =>
    PlayerFactory.build({
      playerId: `player-${id}`,
      gameworldId: mockGameworldId,
      firstName: `First${id}`,
      surname: `Last${id}`,
    });

  // Create mock players array
  const mockPlayers: Player[] = Array.from({ length: 20 }, (_, i) => createMockPlayer(i + 1));

  // Create mock scouting request
  const mockScoutingRequest: ScoutingRequest = {
    type: 'league',
    id: mockLeagueId,
    requestId: 'test-request-id',
    pk: `${mockGameworldId}#${mockTeamId}`,
    teamId: mockTeamId,
    gameworldId: mockGameworldId,
    processAfter: Date.now() - 1000, // Ready to process
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(seededRandom).mockReturnValue(Math.random());
    vi.useFakeTimers({ shouldAdvanceTime: true });
    vi.setSystemTime(new Date('2023-01-01T00:00:00Z')); // Set a fixed date for testing
  });

  it('should process a league scouting request and update scouted players', async () => {
    // Create SQS event with a scouting request
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Set the parsed body directly (middleware would normally parse this)
    event.Records[0].body = mockScoutingRequest;

    // Add repository context that middleware would normally provide
    mockScoutingRepository.scoutRandomPlayersFromLeague.mockResolvedValue(mockPlayers.slice(0, 5));

    // Execute the handler
    await handler(event, mockContext);

    // Verify the repository was called with correct parameters
    expect(mockScoutingRepository.scoutRandomPlayersFromLeague).toHaveBeenCalledWith(
      mockGameworldId,
      mockLeagueId,
      mockTeamId,
      5
    );

    // Verify the repository was called once
    expect(mockScoutingRepository.scoutRandomPlayersFromLeague).toHaveBeenCalledTimes(1);
  });

  it('should update existing scouted players record when one exists', async () => {
    // Create SQS event with a scouting request
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Set the parsed body directly (middleware would normally parse this)
    event.Records[0].body = mockScoutingRequest;

    // Add repository context that middleware would normally provide
    mockScoutingRepository.scoutRandomPlayersFromLeague.mockResolvedValue(mockPlayers.slice(0, 5));

    // Set a fixed date for testing
    const mockDate = new Date('2023-01-01T00:00:00Z');
    vi.spyOn(global.Date, 'now').mockReturnValue(mockDate.getTime());

    // Execute the handler
    await handler(event, mockContext);

    // Verify the repository was called with correct parameters
    expect(mockScoutingRepository.scoutRandomPlayersFromLeague).toHaveBeenCalledWith(
      mockGameworldId,
      mockLeagueId,
      mockTeamId,
      5
    );

    // Verify the repository was called once
    expect(mockScoutingRepository.scoutRandomPlayersFromLeague).toHaveBeenCalledTimes(1);
  });

  it('should handle multiple scouting requests for the same league', async () => {
    // Create SQS event with multiple scouting requests for the same league
    const secondRequest = {
      ...mockScoutingRequest,
      teamId: 'another-team-id',
    };

    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
      {
        body: secondRequest,
      },
    ]) as any;

    // Set the parsed bodies directly (middleware would normally parse this)
    event.Records[0].body = mockScoutingRequest;
    event.Records[1].body = secondRequest;

    // Add repository context that middleware would normally provide
    mockScoutingRepository.scoutRandomPlayersFromLeague.mockResolvedValue(mockPlayers.slice(0, 5));

    // Execute the handler
    await handler(event, mockContext);

    // Verify the repository was called twice (once for each team)
    expect(mockScoutingRepository.scoutRandomPlayersFromLeague).toHaveBeenCalledTimes(2);

    // Verify both calls were for the same league but different teams
    expect(mockScoutingRepository.scoutRandomPlayersFromLeague).toHaveBeenCalledWith(
      mockGameworldId,
      mockLeagueId,
      mockTeamId,
      5
    );
    expect(mockScoutingRepository.scoutRandomPlayersFromLeague).toHaveBeenCalledWith(
      mockGameworldId,
      mockLeagueId,
      'another-team-id',
      5
    );
  });

  it('should handle errors when updating scouted players', async () => {
    // Create SQS event with a scouting request
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Set the parsed body directly (middleware would normally parse this)
    event.Records[0].body = mockScoutingRequest;

    // Add repository context that throws an error
    const mockError = new Error('Database error');
    mockScoutingRepository.scoutRandomPlayersFromLeague.mockRejectedValue(mockError);

    // Execute the handler and expect it to throw
    await expect(handler(event, mockContext)).rejects.toThrow(mockError);
  });

  it('should work normally even if SCOUTED_PLAYERS_TABLE_NAME is not set', async () => {
    // Create SQS event with a scouting request
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Set the parsed body directly (middleware would normally parse this)
    event.Records[0].body = mockScoutingRequest;

    // Add repository context (function now uses repositories, not env vars directly)
    mockScoutingRepository.scoutRandomPlayersFromLeague.mockResolvedValue(mockPlayers.slice(0, 5));

    // Unset the environment variable
    delete process.env.SCOUTED_PLAYERS_TABLE_NAME;

    // Execute the handler - should work normally with repositories
    await handler(event, mockContext);

    // Verify the repository was called
    expect(mockScoutingRepository.scoutRandomPlayersFromLeague).toHaveBeenCalledTimes(1);
  });

  it('should handle pagination when retrieving players from repository', async () => {
    // Create SQS event with a scouting request
    const event = createSqsEvent([
      {
        body: mockScoutingRequest,
      },
    ]) as any;

    // Set the parsed body directly (middleware would normally parse this)
    event.Records[0].body = mockScoutingRequest;

    // Add repository context that handles pagination internally
    mockScoutingRepository.scoutRandomPlayersFromLeague.mockResolvedValue(mockPlayers.slice(0, 5));

    // Execute the handler
    await handler(event, mockContext);

    // Verify the repository was called once (pagination is handled internally)
    expect(mockScoutingRepository.scoutRandomPlayersFromLeague).toHaveBeenCalledTimes(1);
    expect(mockScoutingRepository.scoutRandomPlayersFromLeague).toHaveBeenCalledWith(
      mockGameworldId,
      mockLeagueId,
      mockTeamId,
      5
    );
  });
});

import { League } from '@/entities/League.js';
import { LeagueRules } from '@/entities/LeagueRules.js';
import { generateRandomLeagueName } from '@/functions/generate/random-league-name.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';

import { Gameworld } from '@/entities/Gameworld.js';
import { SQS } from '@/services/sqs/sqs.js';
import { LeagueRepository } from '@/storage-interface/leagues/league-repository.interface.js';
import { GenerateUnattachedPlayersEvent } from '@/types/generated/index.js';
import { LeagueTableCreatedEvent } from '@/types/generated/league-table-created-event.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import { SendMessageCommandOutput } from '@aws-sdk/client-sqs';

interface Body {
  tiers: number;
  teamsPerLeague: number;
  childLeagues: number;
}

export type GenerateGameworldEvent = HttpEvent<Body, void, void>;

interface LeagueLevel {
  rules: LeagueRules;
  childCount: number;
}

// We'll initialize the repository inside the handler function

async function createLeagueHierarchy(
  gameworld: Gameworld,
  body: Body,
  leagueRepository: LeagueRepository
): Promise<{ leagues: League[] }> {
  // Define the hierarchy structure
  const leagueLevels: LeagueLevel[] = [];

  // Generate levels based on number of tiers
  for (let tier = 0; tier < body.tiers; tier++) {
    const rule = new LeagueRules();
    rule.teamCount = body.teamsPerLeague;
    rule.promotionSpots = tier === 0 ? 0 : 1;
    rule.relegationSpots = tier === body.tiers - 1 ? 0 : body.childLeagues;
    leagueLevels.push({
      rules: rule,
      // Last tier has no child leagues
      childCount: tier === body.tiers - 1 ? 0 : body.childLeagues,
    });
  }
  logger.debug('League levels generated', { levels: leagueLevels });

  // Create all leagues recursively and collect them for batch insert
  const allLeagues: League[] = [];

  function createLeagueLevel(
    level: number,
    parentLeague: League | undefined = undefined,
    parentName: string = ''
  ): League[] {
    if (level >= leagueLevels.length) return [];

    logger.debug('Creating leagues for level', { level, leagueLevels });
    const currentLevel = leagueLevels[level]!;
    const childLeagues: League[] = [];

    for (let i = 0; i < currentLevel.childCount; i++) {
      // Generate a name for this league based on its tier and parent
      const leagueName = generateRandomLeagueName(level + 2, parentName, i);

      const league = new League();
      league.gameworld = gameworld;
      league.name = leagueName;
      league.tier = level + 2;
      league.parentLeague = parentLeague;
      league.leagueRules = new LeagueRules(leagueLevels[level + 1]!.rules);
      league.leagueRules.league = league;
      logger.debug('League object created', { league, currentLevel });

      const children = createLeagueLevel(level + 1, league, leagueName);
      children.map((child) => {
        league.leagueChildren.add(child);
      });
      childLeagues.push(league);
      allLeagues.push(league);
    }

    return childLeagues;
  }

  // Create parent league first
  logger.debug('Creating parent league');
  const parentLeagueName = generateRandomLeagueName(1);
  const parentLeague = new League();
  parentLeague.gameworld = gameworld;
  parentLeague.name = parentLeagueName;
  parentLeague.tier = 1;
  parentLeague.parentLeague = undefined;
  parentLeague.leagueRules = new LeagueRules(leagueLevels[0]!.rules);
  parentLeague.leagueRules.league = parentLeague;

  // Then get its children
  logger.debug('Creating child leagues');
  const children = createLeagueLevel(0, parentLeague, parentLeagueName);
  children.map((child) => {
    parentLeague.leagueChildren.add(child);
  });
  allLeagues.push(parentLeague);

  logger.debug('All leagues created');
  // Batch insert all leagues
  if (allLeagues.length > 0) {
    try {
      await leagueRepository.batchCreateLeagues(allLeagues);
      return {
        leagues: allLeagues,
      };
    } catch (error) {
      logger.error('Error inserting leagues', { error });
    }
  }

  return { leagues: [] };
}

const main = async function (httpEvent: GenerateGameworldEvent) {
  logger.debug('Generating gameworld');

  // Get the repository from the context (injected by middleware)
  const { leagueRepository, gameworldRepository } = httpEvent.context.repositories;

  const gameworld = new Gameworld();
  gameworld.endDate = 0;
  await gameworldRepository.createGameworld(gameworld);

  const result = await createLeagueHierarchy(gameworld, httpEvent.body, leagueRepository);

  // Send message directly to SQS queue to generate unattached players for transfer list
  const sqsClient = new SQS({ tracer });
  const unattachedPlayersEvent: GenerateUnattachedPlayersEvent = {
    gameworldId: gameworld.id,
    requiredPlayers: 100,
  };
  await sqsClient.send(
    process.env.UNATTACHED_PLAYERS_QUEUE_URL!,
    JSON.stringify(unattachedPlayersEvent)
  );

  // Send message directly to SQS queue for each league created
  const promises: Promise<SendMessageCommandOutput>[] = [];
  result.leagues.forEach((league) => {
    const event: LeagueTableCreatedEvent = {
      gameworldId: gameworld.id,
      leagueId: league.id,
      tier: league.tier,
      requiredTeams: league.leagueRules.teamCount,
      availableToManage: league.tier >= Math.ceil(httpEvent.body.tiers / 2),
    };

    promises.push(sqsClient.send(process.env.TEAM_QUEUE_URL!, JSON.stringify(event)));
  });
  await Promise.all(promises);

  logger.info('League hierarchy created successfully');

  return Promise.resolve(
    buildResponse(
      200,
      JSON.stringify({
        message: 'Gameworld created successfully',
        gameworld: gameworld.id,
      })
    )
  );
};

export const handler = httpMiddify(main, {});

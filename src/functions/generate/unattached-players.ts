import { Player } from '@/entities/Player.js';
import { TransferListedPlayer } from '@/entities/TransferListedPlayer.js';
import { generatePlayer, getPlayerTemplates } from '@/functions/generate/player.js';
import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { TeamRepository } from '@/storage-interface/teams/team-repository.interface.js';
import { GenerateUnattachedPlayersEvent } from '@/types/generated/index.js';
import { logger } from '@/utils/logger.js';
import { seededRandomIntInRange, setAndReturnSeededRandom } from '@/utils/seeded-random.js';

interface UnattachedPlayer {
  player: Player;
  transferDetails: TransferListedPlayer;
}

function generateTransferDetails(player: Player): TransferListedPlayer {
  const transferList = new TransferListedPlayer();
  // Convert the value to numeric before assigning
  transferList.auctionStartPrice = Number(player.value.toFixed(2));
  transferList.auctionCurrentPrice = Number(player.value.toFixed(2));
  transferList.auctionEndTime = Date.now() + 1000 * 60 * 60 * 24; // 24 hours from now
  transferList.player = player;
  transferList.gameworldId = player.gameworldId;
  // bidHistory is now a Collection that will be initialized by MikroORM

  // Ensure player value is also properly formatted
  player.value = Number(player.value.toFixed(2));

  return transferList;
}

function generateUnattachedPlayer(
  teamRepository: TeamRepository,
  gameworldId: string,
  seed: number
): UnattachedPlayer {
  const playerTemplates = getPlayerTemplates(seededRandomIntInRange(0, 3));
  const template = playerTemplates[seededRandomIntInRange(0, playerTemplates.length - 1)]!;

  const newPlayer = generatePlayer(
    teamRepository,
    gameworldId,
    undefined,
    undefined,
    template.skills,
    template.baseSkill,
    seed
  );

  return {
    player: newPlayer,
    transferDetails: generateTransferDetails(newPlayer),
  };
}

function generateUnattachedPlayers(
  teamRepository: TeamRepository,
  event: GenerateUnattachedPlayersEvent
) {
  const players: UnattachedPlayer[] = [];
  const seed = setAndReturnSeededRandom();

  // Generate players for each template
  for (let i = 0; i < event.requiredPlayers; i++) {
    players.push(generateUnattachedPlayer(teamRepository, event.gameworldId, seed));
  }

  return players;
}

async function main(event: SQSEvent<GenerateUnattachedPlayersEvent>) {
  const { playerRepository, teamRepository } = event.context.repositories;

  const allPlayers: UnattachedPlayer[] = [];
  for (const record of event.Records) {
    const createEvent = record.body;
    logger.debug(
      `Generating ${createEvent.requiredPlayers} players for gameworld ${createEvent.gameworldId}`
    );
    allPlayers.push(...generateUnattachedPlayers(teamRepository, createEvent));
  }

  if (allPlayers.length > 0) {
    //await playerRepository.batchCreatePlayers(allPlayers.map((p) => p.player));
    await playerRepository.batchCreateTransferListedPlayers(
      allPlayers.map((p) => p.transferDetails)
    );
  }
  return;
}

export const handler = sqsMiddify<GenerateUnattachedPlayersEvent>(main, {});

import { Player } from '@/entities/Player.js';
import {
  ATTRIBUTE_MAX,
  ATTRIBUTE_MIN,
  calculatePlayerValue,
  generatePlayerAttributes,
} from '@/functions/generate/player-attributes.js';
import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { TeamRepository } from '@/storage-interface/teams/team-repository.interface.ts';
import { BaseAttributeName } from '@/types/attribute-utils.js';
import { GeneratePlayersEvent } from '@/types/generated/generate-players-event.js';
import { TeamCreatedEvent } from '@/types/generated/index.js';
import { logger } from '@/utils/logger.js';
import {
  seededRandom,
  seededRandomIntInRange,
  setAndReturnSeededRandom,
} from '@/utils/seeded-random.js';
import { v4 as uuidv4 } from 'uuid';
import firstNames from './gb-forenames.json' with { type: 'json' };
import surnames from './gb-surnames.json' with { type: 'json' };

export function generatePlayerWithinRanges(
  event: GeneratePlayersEvent,
  teamRepository: TeamRepository
): Player {
  const player = new Player();
  player.playerId = uuidv4();
  player.gameworldId = event.gameworldId;
  player.team = teamRepository.createFromPK(event.teamId);
  player.seed = setAndReturnSeededRandom();
  player.age = seededRandomIntInRange(event.minAge ?? 17, event.maxAge ?? 38);
  player.firstName = firstNames[Math.floor(seededRandom() * firstNames.length)]!.name;
  player.surname = surnames[Math.floor(seededRandom() * surnames.length)]!.name;
  player.attributes = generatePlayerAttributes(
    seededRandomIntInRange(event.minSkill ?? 1, event.maxSkill ?? 40),
    event.minPotential ?? 1,
    event.maxPotential ?? 40,
    []
  );
  player.value = Number(calculatePlayerValue(player).toFixed(2));
  player.energy = 100;
  player.lastMatchPlayed = 0;
  player.suspendedForGames = 0;
  return player;
}

export function generatePlayer(
  teamRepository: TeamRepository,
  gameworldId: string,
  teamId?: string,
  leagueId?: string,
  templateSkills?: BaseAttributeName[],
  baseSkill?: number,
  seed?: number
): Player {
  // random number between 1 and 10
  const randomSkill = Math.floor(seededRandom() * 10) + 1;
  // three random skills
  let randomSkills: BaseAttributeName[] = [];
  if (!templateSkills) {
    randomSkills = [
      'reflexes',
      'positioning',
      'shotStopping',
      'tackling',
      'marking',
      'heading',
      'finishing',
      'pace',
      'crossing',
      'passing',
      'vision',
      'ballControl',
    ]
      .sort(() => seededRandom() - 0.5)
      .slice(0, 3) as BaseAttributeName[];
  }
  // random age between 17 and 39
  const randomAge = Math.floor(seededRandom() * 23) + 17;

  const player = new Player();
  player.gameworldId = gameworldId;
  player.team = teamId ? teamRepository.createFromPK(teamId) : undefined;
  player.playerId = uuidv4();
  player.firstName = firstNames[Math.floor(seededRandom() * firstNames.length)]!.name;
  player.surname = surnames[Math.floor(seededRandom() * surnames.length)]!.name;
  // Ensure we have a non-undefined array of skills
  const skills: BaseAttributeName[] = templateSkills || randomSkills;

  player.attributes = generatePlayerAttributes(
    baseSkill ?? randomSkill,
    ATTRIBUTE_MIN,
    ATTRIBUTE_MAX,
    skills
  );
  player.age = randomAge;
  player.seed = seed!;
  player.value = Number(calculatePlayerValue(player).toFixed(2));
  player.energy = 100;
  player.lastMatchPlayed = 0;
  player.suspendedForGames = 0;
  return player;
}

export interface PlayerTemplate {
  skills: BaseAttributeName[];
  baseSkill: number;
}

const highBaseSkillAtTier = [28, 24, 18, 12];
const lowBaseSkillAtTier = [19, 15, 10, 6];

export function getPlayerTemplates(tier: number): PlayerTemplate[] {
  return [
    // goalkeepers (1 decent, 1 poor)
    {
      skills: ['reflexes', 'positioning', 'shotStopping'],
      baseSkill: highBaseSkillAtTier[tier - 1]!,
    },
    {
      skills: ['reflexes', 'positioning', 'shotStopping'],
      baseSkill: lowBaseSkillAtTier[tier - 1]!,
    },
    // defenders (1 decent, 2 poor)
    {
      skills: ['tackling', 'marking', 'heading'],
      baseSkill: highBaseSkillAtTier[tier - 1]!,
    },
    {
      skills: ['tackling', 'marking', 'heading'],
      baseSkill: lowBaseSkillAtTier[tier - 1]!,
    },
    {
      skills: ['tackling', 'marking', 'heading'],
      baseSkill: lowBaseSkillAtTier[tier - 1]!,
    },
    // attackers (1 decent, 2 poor)
    {
      skills: ['finishing', 'pace', 'crossing'],
      baseSkill: highBaseSkillAtTier[tier - 1]!,
    },
    {
      skills: ['finishing', 'pace', 'crossing'],
      baseSkill: lowBaseSkillAtTier[tier - 1]!,
    },
    {
      skills: ['finishing', 'pace', 'crossing'],
      baseSkill: lowBaseSkillAtTier[tier - 1]!,
    },
    // midfielders (1 decent, 2 poor)
    {
      skills: ['passing', 'vision', 'ballControl'],
      baseSkill: highBaseSkillAtTier[tier - 1]!,
    },
    {
      skills: ['passing', 'vision', 'ballControl'],
      baseSkill: lowBaseSkillAtTier[tier - 1]!,
    },
    {
      skills: ['passing', 'vision', 'ballControl'],
      baseSkill: lowBaseSkillAtTier[tier - 1]!,
    },
  ];
}

function generatePlayers(teamRepository: TeamRepository, team: TeamCreatedEvent) {
  const players: Player[] = [];
  const playerTemplates = getPlayerTemplates(team.tier);

  // save the seed so we can generate the same players again for testing
  const seed = setAndReturnSeededRandom();

  for (let i = 0; i < team.requiredPlayers; i++) {
    if (playerTemplates.length > i) {
      const template = playerTemplates[i]!;
      players.push(
        generatePlayer(
          teamRepository,
          team.gameworldId,
          team.teamId,
          team.leagueId,
          template.skills,
          template.baseSkill,
          seed
        )
      );
      continue;
    }
    players.push(
      generatePlayer(
        teamRepository,
        team.gameworldId,
        team.teamId,
        team.leagueId,
        undefined,
        undefined,
        seed
      )
    );
  }

  return players;
}

/**
 * Generate players in response to an SQS event
 * @param event
 */
async function main(event: SQSEvent<TeamCreatedEvent>) {
  const allPlayers = [];

  const { playerRepository, teamRepository } = event.context.repositories;

  for (const record of event.Records) {
    const team = record.body;
    logger.debug(`Generating ${team.requiredPlayers} players for team ${team.teamId}`);
    allPlayers.push(...generatePlayers(teamRepository, team));
  }

  await playerRepository.batchCreatePlayers(allPlayers);
  return;
}

export const handler = sqsMiddify<TeamCreatedEvent>(main, {});

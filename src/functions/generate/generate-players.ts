import { Player } from '@/entities/Player.ts';
import { generatePlayerWithinRanges } from '@/functions/generate/player.js';
import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { NotificationManager } from '@/services/notifications/NotificationManager.js';
import { GeneratePlayersEvent } from '@/types/generated/generate-players-event.js';
import { logger } from '@/utils/logger.js';

const notificationManager = NotificationManager.getInstance();

async function main(event: SQSEvent<GeneratePlayersEvent>) {
  const { playerRepository, teamRepository } = event.context.repositories;

  const allPlayers: Player[] = [];
  for (const record of event.Records) {
    const createEvent = record.body;
    logger.debug(
      `Generating ${createEvent.requiredPlayers} players for team ${createEvent.teamId}`
    );
    for (let i = 0; i < createEvent.requiredPlayers; i++) {
      allPlayers.push(generatePlayerWithinRanges(createEvent, teamRepository));
    }
    if (createEvent.managerId) {
      await notificationManager.loadManagerPreferences(
        createEvent.managerId,
        event.context.repositories
      );
      await notificationManager.sendYouthPlayerNotification(allPlayers);
    }
  }

  if (allPlayers.length > 0) {
    await playerRepository.batchCreatePlayers(allPlayers);
  }
  return;
}

export const handler = sqsMiddify<GeneratePlayersEvent>(main, {});

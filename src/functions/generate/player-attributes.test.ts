import { describe, expect, it } from 'vitest';

import { Player } from '@/entities/Player.js';
import { PlayerAttributes } from '@/entities/PlayerAttributes.js';
import { BaseAttributeName } from '@/types/attribute-utils.js';
import { setRandomSeed } from '@/utils/seeded-random.js';
import {
  calculatePlayerValue,
  formatPlayerAttributes,
  generatePlayerAttributes,
} from './player-attributes.js';

describe('Player Attributes Generation', () => {
  it('generates attributes with correct focus categories', () => {
    const skillLevel = 20;
    const minPotential = 20;
    const maxPotential = 30;
    const focusCategories: BaseAttributeName[] = ['finishing', 'pace'];
    const attributes = generatePlayerAttributes(
      skillLevel,
      minPotential,
      maxPotential,
      focusCategories
    );

    // Focus categories should have higher values
    expect(attributes.finishingCurrent).toBeGreaterThanOrEqual(1);
    expect(attributes.finishingCurrent).toBeLessThanOrEqual(40);
    expect(attributes.paceCurrent).toBeGreaterThanOrEqual(1);
    expect(attributes.paceCurrent).toBeLessThanOrEqual(40);
  });

  it('generates attributes within valid range', () => {
    const skillLevel = 10;
    const minPotential = 10;
    const maxPotential = 20;
    const focusCategories: BaseAttributeName[] = [];
    const attributes = generatePlayerAttributes(
      skillLevel,
      minPotential,
      maxPotential,
      focusCategories
    );

    // Check all attributes are within valid range
    const baseAttributes: BaseAttributeName[] = [
      'reflexes',
      'positioning',
      'shotStopping',
      'tackling',
      'marking',
      'heading',
      'finishing',
      'pace',
      'crossing',
      'passing',
      'vision',
      'ballControl',
    ];

    baseAttributes.forEach((attr) => {
      const currentProp = `${attr}Current` as keyof PlayerAttributes;
      const potentialProp = `${attr}Potential` as keyof PlayerAttributes;

      expect(attributes[currentProp]).toBeGreaterThanOrEqual(1);
      expect(attributes[currentProp]).toBeLessThanOrEqual(40);
      expect(attributes[potentialProp]).toBeGreaterThanOrEqual(Number(attributes[currentProp]));
      expect(attributes[potentialProp]).toBeLessThanOrEqual(40);
    });
  });

  it('generates reproducible attributes with the same seed', () => {
    const skillLevel = 15;
    const minPotential = 15;
    const maxPotential = 25;
    const focusCategories: BaseAttributeName[] = ['vision'];

    // Set the same seed for both calls
    setRandomSeed(12345);
    const attributes1 = generatePlayerAttributes(
      skillLevel,
      minPotential,
      maxPotential,
      focusCategories
    );

    setRandomSeed(12345);
    const attributes2 = generatePlayerAttributes(
      skillLevel,
      minPotential,
      maxPotential,
      focusCategories
    );

    // Compare individual properties instead of the whole object
    expect(attributes1.visionCurrent).toBe(attributes2.visionCurrent);
    expect(attributes1.visionPotential).toBe(attributes2.visionPotential);
    expect(attributes1.finishingCurrent).toBe(attributes2.finishingCurrent);
    expect(attributes1.finishingPotential).toBe(attributes2.finishingPotential);
    expect(attributes1.stamina).toBe(attributes2.stamina);
  });

  it('applies higher weights to focus categories', () => {
    const skillLevel = 15;
    const minPotential = 15;
    const maxPotential = 25;
    const focusCategories: BaseAttributeName[] = ['finishing', 'pace', 'crossing'];
    const attributes = generatePlayerAttributes(
      skillLevel,
      minPotential,
      maxPotential,
      focusCategories
    );

    // Calculate average for focus categories
    const focusAvg =
      (attributes.finishingCurrent + attributes.paceCurrent + attributes.crossingCurrent) / 3;

    // Calculate average for non-focus categories
    const nonFocusAvg =
      (attributes.reflexesCurrent +
        attributes.positioningCurrent +
        attributes.shotStoppingCurrent +
        attributes.tacklingCurrent +
        attributes.markingCurrent +
        attributes.headingCurrent +
        attributes.passingCurrent +
        attributes.visionCurrent +
        attributes.ballControlCurrent) /
      9;

    // Focus categories should generally have higher values
    expect(focusAvg).toBeGreaterThanOrEqual(nonFocusAvg * 0.8);
  });

  it('handles edge case with very low skill level', () => {
    const skillLevel = 1;
    const minPotential = 1;
    const maxPotential = 10;
    const focusCategories: BaseAttributeName[] = [];
    const attributes = generatePlayerAttributes(
      skillLevel,
      minPotential,
      maxPotential,
      focusCategories
    );

    // Even with low skill level, attributes should be at least 1
    const baseAttributes: BaseAttributeName[] = [
      'reflexes',
      'positioning',
      'shotStopping',
      'tackling',
      'marking',
      'heading',
      'finishing',
      'pace',
      'crossing',
      'passing',
      'vision',
      'ballControl',
    ];

    baseAttributes.forEach((attr) => {
      const currentProp = `${attr}Current` as keyof PlayerAttributes;
      expect(attributes[currentProp]).toBeGreaterThanOrEqual(1);
    });
  });

  it('handles edge case with very high skill level', () => {
    const skillLevel = 40;
    const minPotential = 40;
    const maxPotential = 40;
    const focusCategories: BaseAttributeName[] = [];
    const attributes = generatePlayerAttributes(
      skillLevel,
      minPotential,
      maxPotential,
      focusCategories
    );

    // With high skill level, attributes should not exceed 40
    const baseAttributes: BaseAttributeName[] = [
      'reflexes',
      'positioning',
      'shotStopping',
      'tackling',
      'marking',
      'heading',
      'finishing',
      'pace',
      'crossing',
      'passing',
      'vision',
      'ballControl',
    ];

    baseAttributes.forEach((attr) => {
      const currentProp = `${attr}Current` as keyof PlayerAttributes;
      const potentialProp = `${attr}Potential` as keyof PlayerAttributes;

      expect(attributes[currentProp]).toBeLessThanOrEqual(40);
      expect(attributes[potentialProp]).toBeLessThanOrEqual(40);
    });
  });
});

describe('Player Value Calculation', () => {
  // Helper function to create a Player entity with specified attributes
  function createTestPlayer(age: number, current: number, potential: number): Player {
    const player = new Player();
    player.age = age;

    const attributes = new PlayerAttributes();
    attributes.reflexesCurrent = current;
    attributes.reflexesPotential = potential;
    attributes.positioningCurrent = current;
    attributes.positioningPotential = potential;
    attributes.shotStoppingCurrent = current;
    attributes.shotStoppingPotential = potential;
    attributes.tacklingCurrent = current;
    attributes.tacklingPotential = potential;
    attributes.markingCurrent = current;
    attributes.markingPotential = potential;
    attributes.headingCurrent = current;
    attributes.headingPotential = potential;
    attributes.finishingCurrent = current;
    attributes.finishingPotential = potential;
    attributes.paceCurrent = current;
    attributes.pacePotential = potential;
    attributes.crossingCurrent = current;
    attributes.crossingPotential = potential;
    attributes.passingCurrent = current;
    attributes.passingPotential = potential;
    attributes.visionCurrent = current;
    attributes.visionPotential = potential;
    attributes.ballControlCurrent = current;
    attributes.ballControlPotential = potential;

    player.attributes = attributes;
    return player;
  }

  it('calculates value for a young player with high potential', () => {
    const player = createTestPlayer(20, 30, 35);

    const value = calculatePlayerValue(player);
    expect(value).toEqual(1687500);
  });

  it('calculates minimum value for a player with low attributes', () => {
    const player = createTestPlayer(40, 1, 1);

    const value = calculatePlayerValue(player);
    expect(value).toBeLessThan(1000);
  });

  it('calculates a sensible value for an OK level 4 attacker', () => {
    const player = new Player();
    player.age = 26;

    const attributes = new PlayerAttributes();
    // Goalkeeper attributes
    attributes.reflexesCurrent = 2;
    attributes.reflexesPotential = 2;
    attributes.positioningCurrent = 2;
    attributes.positioningPotential = 2;
    attributes.shotStoppingCurrent = 2;
    attributes.shotStoppingPotential = 2;
    // Defender attributes
    attributes.tacklingCurrent = 4;
    attributes.tacklingPotential = 4;
    attributes.markingCurrent = 4;
    attributes.markingPotential = 4;
    attributes.headingCurrent = 4;
    attributes.headingPotential = 4;
    // Attacker attributes
    attributes.finishingCurrent = 9;
    attributes.finishingPotential = 9;
    attributes.paceCurrent = 9;
    attributes.pacePotential = 9;
    attributes.crossingCurrent = 9;
    attributes.crossingPotential = 9;
    // Midfielder attributes
    attributes.passingCurrent = 4;
    attributes.passingPotential = 4;
    attributes.visionCurrent = 4;
    attributes.visionPotential = 4;
    attributes.ballControlCurrent = 4;
    attributes.ballControlPotential = 4;

    player.attributes = attributes;

    const value = calculatePlayerValue(player);
    expect(value).toBeGreaterThan(37500);
    expect(value).toBeLessThan(75000);
  });

  it('calculates value for an older player with high current attributes', () => {
    const player = createTestPlayer(35, 40, 40);

    const value = calculatePlayerValue(player);
    expect(value).toEqual(2250000);
  });

  it('calculates a higher value for a young player than an old player with the same attributes', () => {
    const player1 = createTestPlayer(20, 40, 40);
    const player2 = createTestPlayer(35, 40, 40);

    const value1 = calculatePlayerValue(player1);
    const value2 = calculatePlayerValue(player2);
    expect(value1).toBeGreaterThan(value2);
  });

  it('returns 0 for a player with no attributes', () => {
    const player = new Player();
    player.age = 25;
    // No attributes set

    const value = calculatePlayerValue(player);
    expect(value).toEqual(0);
  });

  it('calculates higher value for players with specialized attributes', () => {
    // Create a specialized goalkeeper
    const goalkeeper = new Player();
    goalkeeper.age = 25;

    const gkAttributes = new PlayerAttributes();
    // High goalkeeper attributes
    gkAttributes.reflexesCurrent = 35;
    gkAttributes.reflexesPotential = 38;
    gkAttributes.positioningCurrent = 32;
    gkAttributes.positioningPotential = 35;
    gkAttributes.shotStoppingCurrent = 30;
    gkAttributes.shotStoppingPotential = 33;
    // Low other attributes
    gkAttributes.tacklingCurrent = 10;
    gkAttributes.tacklingPotential = 12;
    gkAttributes.markingCurrent = 8;
    gkAttributes.markingPotential = 10;
    gkAttributes.headingCurrent = 12;
    gkAttributes.headingPotential = 15;
    gkAttributes.finishingCurrent = 5;
    gkAttributes.finishingPotential = 7;
    gkAttributes.paceCurrent = 10;
    gkAttributes.pacePotential = 12;
    gkAttributes.crossingCurrent = 8;
    gkAttributes.crossingPotential = 10;
    gkAttributes.passingCurrent = 15;
    gkAttributes.passingPotential = 18;
    gkAttributes.visionCurrent = 12;
    gkAttributes.visionPotential = 15;
    gkAttributes.ballControlCurrent = 10;
    gkAttributes.ballControlPotential = 12;

    goalkeeper.attributes = gkAttributes;

    // Create a balanced player with same average attributes
    const balanced = new Player();
    balanced.age = 25;

    const balAttributes = new PlayerAttributes();
    // All attributes around 16-17
    balAttributes.reflexesCurrent = 16;
    balAttributes.reflexesPotential = 19;
    balAttributes.positioningCurrent = 16;
    balAttributes.positioningPotential = 19;
    balAttributes.shotStoppingCurrent = 16;
    balAttributes.shotStoppingPotential = 19;
    balAttributes.tacklingCurrent = 16;
    balAttributes.tacklingPotential = 19;
    balAttributes.markingCurrent = 16;
    balAttributes.markingPotential = 19;
    balAttributes.headingCurrent = 16;
    balAttributes.headingPotential = 19;
    balAttributes.finishingCurrent = 16;
    balAttributes.finishingPotential = 19;
    balAttributes.paceCurrent = 16;
    balAttributes.pacePotential = 19;
    balAttributes.crossingCurrent = 16;
    balAttributes.crossingPotential = 19;
    balAttributes.passingCurrent = 16;
    balAttributes.passingPotential = 19;
    balAttributes.visionCurrent = 16;
    balAttributes.visionPotential = 19;
    balAttributes.ballControlCurrent = 16;
    balAttributes.ballControlPotential = 19;

    balanced.attributes = balAttributes;

    // The specialized player should be worth more due to the squared calculation
    const gkValue = calculatePlayerValue(goalkeeper);
    const balValue = calculatePlayerValue(balanced);

    expect(gkValue).toBeGreaterThan(balValue);
  });
});

describe('Player Attributes Formatting', () => {
  it('formats player attributes correctly', () => {
    const attributes = new PlayerAttributes();

    // Set some test values
    attributes.reflexesCurrent = 15;
    attributes.reflexesPotential = 20;
    attributes.positioningCurrent = 12;
    attributes.positioningPotential = 18;
    attributes.shotStoppingCurrent = 10;
    attributes.shotStoppingPotential = 15;

    attributes.tacklingCurrent = 8;
    attributes.tacklingPotential = 12;
    attributes.markingCurrent = 9;
    attributes.markingPotential = 14;
    attributes.headingCurrent = 11;
    attributes.headingPotential = 16;

    attributes.finishingCurrent = 20;
    attributes.finishingPotential = 25;
    attributes.paceCurrent = 18;
    attributes.pacePotential = 22;
    attributes.crossingCurrent = 16;
    attributes.crossingPotential = 20;

    attributes.passingCurrent = 14;
    attributes.passingPotential = 19;
    attributes.visionCurrent = 13;
    attributes.visionPotential = 17;
    attributes.ballControlCurrent = 15;
    attributes.ballControlPotential = 20;

    const formatted = formatPlayerAttributes(attributes);

    // Check that the output is a string
    expect(typeof formatted).toBe('string');

    // Check that it contains all attribute names
    expect(formatted).toContain('GOALKEEPING');
    expect(formatted).toContain('DEFENDING');
    expect(formatted).toContain('ATTACKING');
    expect(formatted).toContain('MIDFIELD');

    // Check that it contains all attribute values
    expect(formatted).toContain('reflexes');
    expect(formatted).toContain('15|20'); // reflexes values
    expect(formatted).toContain('positioning');
    expect(formatted).toContain('12|18'); // positioning values

    // Check that it has the right number of lines (4 lines: headers + 3 attributes per category)
    const lines = formatted.trim().split('\n');
    expect(lines.length).toBe(4);
  });

  it('aligns columns correctly in the formatted output', () => {
    const attributes = new PlayerAttributes();

    // Set all attributes to the same value for simplicity
    const baseAttributes: BaseAttributeName[] = [
      'reflexes',
      'positioning',
      'shotStopping',
      'tackling',
      'marking',
      'heading',
      'finishing',
      'pace',
      'crossing',
      'passing',
      'vision',
      'ballControl',
    ];

    baseAttributes.forEach((attr) => {
      const currentProp = `${attr}Current` as keyof PlayerAttributes;
      const potentialProp = `${attr}Potential` as keyof PlayerAttributes;

      // Use type assertion to resolve the 'never' type error
      (attributes[currentProp] as number) = 10;
      (attributes[potentialProp] as number) = 15;
    });

    const formatted = formatPlayerAttributes(attributes);
    const lines = formatted.trim().split('\n');

    // Check that the output has 4 lines (header + 3 attributes for each category)
    expect(lines.length).toBe(4);

    // Check that each column starts at the same position in each line
    // by checking for consistent spacing
    const headerPositions = [
      lines[0]?.indexOf('GOALKEEPING') ?? -1,
      lines[0]?.indexOf('DEFENDING') ?? -1,
      lines[0]?.indexOf('MIDFIELD') ?? -1,
      lines[0]?.indexOf('ATTACKING') ?? -1,
    ];

    // Verify that all headers are present and properly spaced
    expect(headerPositions[0]).toBeGreaterThanOrEqual(0);
    expect(headerPositions[1] ?? -1).toBeGreaterThan(headerPositions[0] ?? -1);
    expect(headerPositions[2] ?? -1).toBeGreaterThan(headerPositions[1] ?? -1);
    expect(headerPositions[3] ?? -1).toBeGreaterThan(headerPositions[2] ?? -1);
  });

  it('handles single-digit and double-digit values correctly', () => {
    const attributes = new PlayerAttributes();

    // Mix of single and double-digit values
    attributes.reflexesCurrent = 5;
    attributes.reflexesPotential = 10;
    attributes.positioningCurrent = 15;
    attributes.positioningPotential = 20;
    attributes.shotStoppingCurrent = 25;
    attributes.shotStoppingPotential = 30;

    // Set remaining attributes to avoid undefined values
    attributes.tacklingCurrent = 10;
    attributes.tacklingPotential = 15;
    attributes.markingCurrent = 10;
    attributes.markingPotential = 15;
    attributes.headingCurrent = 10;
    attributes.headingPotential = 15;
    attributes.finishingCurrent = 10;
    attributes.finishingPotential = 15;
    attributes.paceCurrent = 10;
    attributes.pacePotential = 15;
    attributes.crossingCurrent = 10;
    attributes.crossingPotential = 15;
    attributes.passingCurrent = 10;
    attributes.passingPotential = 15;
    attributes.visionCurrent = 10;
    attributes.visionPotential = 15;
    attributes.ballControlCurrent = 10;
    attributes.ballControlPotential = 15;

    const formatted = formatPlayerAttributes(attributes);

    // Check that single-digit values are padded correctly
    expect(formatted).toContain(' 5|10'); // reflexes values
    expect(formatted).toContain('15|20'); // positioning values
    expect(formatted).toContain('25|30'); // shotStopping values
  });
});

describe('PlayerAttributes generation edge cases and invariants', () => {
  it('ensures potential is never less than current and within min/max bounds', () => {
    for (let i = 0; i < 20; i++) {
      const skillLevel = 10 + i;
      const minPotential = 15;
      const maxPotential = 25;
      const attributes = generatePlayerAttributes(skillLevel, minPotential, maxPotential, []);
      const baseAttributes: BaseAttributeName[] = [
        'reflexes',
        'positioning',
        'shotStopping',
        'tackling',
        'marking',
        'heading',
        'finishing',
        'pace',
        'crossing',
        'passing',
        'vision',
        'ballControl',
      ];
      baseAttributes.forEach((attr) => {
        const current = attributes[`${attr}Current` as keyof PlayerAttributes] as number;
        const potential = attributes[`${attr}Potential` as keyof PlayerAttributes] as number;
        expect(potential).toBeGreaterThanOrEqual(current + 0); // allow equal if maxed
        expect(potential).toBeGreaterThanOrEqual(minPotential);
        expect(potential).toBeLessThanOrEqual(maxPotential);
      });
    }
  });

  it('focus category attributes are statistically higher than non-focus', () => {
    const focus: BaseAttributeName = 'finishing';
    let focusSum = 0,
      nonFocusSum = 0,
      runs = 100;
    for (let i = 0; i < runs; i++) {
      const attrs = generatePlayerAttributes(20, 20, 30, [focus]);
      focusSum += attrs[`${focus}Current` as keyof PlayerAttributes] as number;
      const nonFocusAttrs = [
        'reflexes',
        'positioning',
        'shotStopping',
        'tackling',
        'marking',
        'heading',
        'pace',
        'crossing',
        'passing',
        'vision',
        'ballControl',
      ].map((attr) => attrs[`${attr}Current` as keyof PlayerAttributes] as number);
      nonFocusSum += nonFocusAttrs.reduce((a, b) => a + b, 0) / nonFocusAttrs.length;
    }
    expect(focusSum / runs).toBeGreaterThan(nonFocusSum / runs);
  });

  it('handles minPotential > maxPotential gracefully', () => {
    const skillLevel = 10;
    const minPotential = 30;
    const maxPotential = 20;
    const attributes = generatePlayerAttributes(skillLevel, minPotential, maxPotential, []);
    const baseAttributes: BaseAttributeName[] = [
      'reflexes',
      'positioning',
      'shotStopping',
      'tackling',
      'marking',
      'heading',
      'finishing',
      'pace',
      'crossing',
      'passing',
      'vision',
      'ballControl',
    ];
    baseAttributes.forEach((attr) => {
      const current = attributes[`${attr}Current` as keyof PlayerAttributes] as number;
      const potential = attributes[`${attr}Potential` as keyof PlayerAttributes] as number;
      expect(potential).toBeGreaterThanOrEqual(current);
      expect(potential).toBeGreaterThanOrEqual(minPotential);
      expect(potential).toBeLessThanOrEqual(40);
    });
  });

  it('stamina is always between 0 and 1', () => {
    for (let i = 0; i < 20; i++) {
      const attrs = generatePlayerAttributes(20, 20, 30, []);
      expect(attrs.stamina).toBeGreaterThanOrEqual(0);
      expect(attrs.stamina).toBeLessThanOrEqual(1);
    }
  });

  it('all attributes are set and are numbers', () => {
    const attrs = generatePlayerAttributes(20, 20, 30, []);
    const baseAttributes: BaseAttributeName[] = [
      'reflexes',
      'positioning',
      'shotStopping',
      'tackling',
      'marking',
      'heading',
      'finishing',
      'pace',
      'crossing',
      'passing',
      'vision',
      'ballControl',
    ];
    baseAttributes.forEach((attr) => {
      expect(typeof attrs[`${attr}Current` as keyof PlayerAttributes]).toBe('number');
      expect(typeof attrs[`${attr}Potential` as keyof PlayerAttributes]).toBe('number');
    });
    expect(typeof attrs.stamina).toBe('number');
  });
});

import { Fixture } from '@/entities/Fixture.js';
import { generateFixtures } from '@/functions/generate/fixtures.js';
import { Repositories } from '@/middleware/database/types.js';
import { eventMiddify } from '@/middleware/event/index.js';
import { EventHandler } from '@/middleware/event/types.js';
import { logger } from '@/utils/logger.js';

// Define the event type for the fixtures lambda
interface GenerateFixturesEvent {
  gameworldId: string;
  leagueId: string;
}

/**
 * Generate fixtures for a league
 * @param event Event containing gameworld ID and league ID
 */
const main: EventHandler<GenerateFixturesEvent, void> = async function (event) {
  const { gameworldRepository } = event.context.repositories;

  const mainStart = Date.now();
  logger.debug('Starting fixtures generation handler');

  // Process the event directly
  const result = await generateFixturesForLeague(event, event.context.repositories);

  logger.debug('Fixtures generation completed', {
    totalFixtures: result.fixtures.length,
    duration: Date.now() - mainStart,
  });

  // set the season end date based on 24 hours after the last fixture date
  const endDate =
    result.fixtures.reduce((acc, fixture) => {
      return Math.max(acc, fixture.date);
    }, 0) +
    24 * 60 * 60 * 1000;
  await gameworldRepository.updateGameworldEndDate(event.gameworldId, endDate);

  return;
};

/**
 * Generate fixtures for a specific league
 * @param body Event body containing gameworld ID and league ID
 * @param repositories Repositories from event context
 * @returns Object containing the generated fixtures
 */
async function generateFixturesForLeague(
  body: GenerateFixturesEvent,
  { teamRepository, fixtureRepository, leagueRepository }: Repositories
): Promise<{ fixtures: Fixture[] }> {
  const startTime = Date.now();
  logger.debug('Starting fixtures generation for league', {
    leagueId: body.leagueId,
    gameworldId: body.gameworldId,
    timestamp: startTime,
  });

  try {
    // Get teams for the league
    const teamsStart = Date.now();
    const teams = await teamRepository.getTeamsByLeague(body.leagueId);
    logger.debug('Teams retrieved for league', {
      leagueId: body.leagueId,
      teamCount: teams.length,
      duration: Date.now() - teamsStart,
    });

    if (teams.length === 0) {
      logger.warn('No teams found for league', {
        leagueId: body.leagueId,
        gameworldId: body.gameworldId,
      });
      return { fixtures: [] };
    }

    // Generate fixtures
    const fixturesStart = Date.now();
    logger.debug('Starting fixtures generation');
    const fixtures = generateFixtures(teamRepository, leagueRepository, teams);
    logger.debug('Fixtures generated', {
      count: fixtures.length,
      duration: Date.now() - fixturesStart,
    });

    // Insert fixtures
    const fixturesInsertStart = Date.now();
    await fixtureRepository.batchInsertFixtures(fixtures);
    logger.debug('Fixtures insertion completed', {
      duration: Date.now() - fixturesInsertStart,
    });

    logger.debug('Fixtures generation process completed', {
      totalDuration: Date.now() - startTime,
    });

    return { fixtures };
  } catch (error) {
    logger.error('Fixtures generation process failed', {
      error,
      leagueId: body.leagueId,
      gameworldId: body.gameworldId,
      duration: Date.now() - startTime,
    });
    return { fixtures: [] };
  }
}

export const handler = eventMiddify(main);

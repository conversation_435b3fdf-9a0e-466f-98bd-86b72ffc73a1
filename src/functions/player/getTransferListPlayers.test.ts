import { TransferListedPlayer } from '@/entities/TransferListedPlayer.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import { PlayerFactory } from '@/testing/factories/playerFactory.js';
import { mockTransferRepository, resetAllRepositoryMocks } from '@/testing/mockRepositories.js';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './getTransferListPlayers.js';

describe('Get Transfer List Players Handler', () => {
  const mockContext = {} as any;

  function createMockPlayer(override: Partial<TransferListedPlayer> = {}): TransferListedPlayer {
    const mockPlayer = PlayerFactory.build({});

    return {
      id: 'transfer-listed-player-id',
      player: mockPlayer,
      gameworldId: override.gameworldId || '',
      auctionStartPrice: 0,
      auctionCurrentPrice: 0,
      auctionListingCounter: 0,
      createdAt: 0,
      getHighestBid: vi.fn().mockReturnValue(null),
      isExpired: vi.fn().mockReturnValue(false),
      getWinningBid: vi.fn().mockReturnValue(null),
      auctionEndTime: 0,
      bidHistory: { getItems: () => [] } as any,
      ...override,
    };
  }

  beforeEach(() => {
    resetAllRepositoryMocks();
    process.env.TRANSFER_LISTED_PLAYERS_TABLE_NAME = 'transfer-listed-players-table';
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should return players with default limit when no parameters provided', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld' },
    });

    vi.mocked(mockTransferRepository.getTransferListedPlayers).mockResolvedValue([
      createMockPlayer(),
      createMockPlayer(),
      createMockPlayer(),
    ]);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(body.players).toHaveLength(3);
    expect(mockTransferRepository.getTransferListedPlayers).toHaveBeenCalledWith(
      'test-gameworld',
      25,
      undefined
    );
  });

  it('should respect custom limit parameter', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld' },
      queryStringParameters: { limit: '2' },
    });

    vi.mocked(mockTransferRepository.getTransferListedPlayers).mockResolvedValue([
      createMockPlayer(),
      createMockPlayer(),
    ]);

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(200);
    expect(mockTransferRepository.getTransferListedPlayers).toHaveBeenCalledWith(
      'test-gameworld',
      2,
      undefined
    );
  });

  it('should handle pagination with lastEvaluatedKey', async () => {
    const lastEvaluatedKeyBase64 = Buffer.from(
      JSON.stringify({
        playerId: '2',
        gameworldId: 'test-gameworld',
      })
    ).toString('base64');

    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld' },
      queryStringParameters: { lastEvaluatedKey: lastEvaluatedKeyBase64 },
    });

    vi.mocked(mockTransferRepository.getTransferListedPlayers).mockResolvedValue([
      createMockPlayer(),
    ]);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(mockTransferRepository.getTransferListedPlayers).toHaveBeenCalledWith(
      'test-gameworld',
      25,
      lastEvaluatedKeyBase64
    );
  });

  it('should handle invalid lastEvaluatedKey', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld' },
      queryStringParameters: { lastEvaluatedKey: 'invalid-base64' },
    });

    vi.mocked(mockTransferRepository.getTransferListedPlayers).mockResolvedValue([]);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(404);
    expect(body.error).toBe('No players found');
  });

  it('should handle case when no players are found', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld' },
    });

    vi.mocked(mockTransferRepository.getTransferListedPlayers).mockResolvedValue([]);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(404);
    expect(body.error).toBe('No players found');
  });

  it('should handle case when query returns undefined', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld' },
    });

    vi.mocked(mockTransferRepository.getTransferListedPlayers).mockResolvedValue(null);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(404);
    expect(body.error).toBe('No players found');
  });
});

import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import createSqsEvent from '@/testing/createSqsEvent.js';
import { Lambda } from '@/utils/lambda.ts';
import { beforeEach, describe, vi } from 'vitest';

// Mock SQS middleware before importing the handler
vi.mock('@/middleware/sqs/index.js', () => ({
  sqsMiddify: vi.fn((handler) => handler),
}));

const team2Promise = import(
  '../../testing/mockData/team/fed087e2-0b19-45f8-82e8-5bcf1df04212.json',
  {
    with: { type: 'json' },
  }
);
const team1Promise = import(
  '../../testing/mockData/team/b1e06081-9fa4-4ae2-9f9a-91578d509be3.json',
  {
    with: { type: 'json' },
  }
);

vi.mock('@/services/database/dynamo/dynamo-db-service');
vi.mock('@/utils/logger', () => ({
  logger: {
    debug: (...args: any[]) => console.log(...args),
    error: (...args: any[]) => console.log(...args),
    info: (...args: any[]) => console.log(...args),
    warn: (...args: any[]) => console.log(...args),
    trace: (...args: any[]) => console.log(...args),
    local: (...args: any[]) => console.log(...args),
    addContext: vi.fn(),
    logEventIfEnabled: vi.fn(),
    clearBuffer: vi.fn(),
    refreshSampleRateCalculation: vi.fn(),
  },
}));
vi.mock('@/utils/tracer');

describe('simulateFixtures', () => {
  const context = {} as any;
  const sqsEvent = createSqsEvent([
    {
      body: {
        date: 1741165200000,
        gameworldId: '21c262c5-ba56-4e82-97b6-7a2456981820',
        fixtureId: 'da7d690b-55a1-45df-872d-cbf365457184',
        leagueId: '73c6b3a1-7ee3-4e35-a90b-d40364b81fa5',
        awayTeamName: 'Grumpy Badger Boot Room',
        gameworldId_leagueId:
          '21c262c5-ba56-4e82-97b6-7a2456981820#73c6b3a1-7ee3-4e35-a90b-d40364b81fa5',
        homeTeamName: 'Ingleley United',
        homeTeamId: 'b1e06081-9fa4-4ae2-9f9a-91578d509be3',
        awayTeamId: 'fed087e2-0b19-45f8-82e8-5bcf1df04212',
      },
    },
  ]) as any;

  // Add repository context that middleware would normally provide
  sqsEvent.context = {
    repositories: {
      fixtureRepository: {
        updateFixtureResult: vi.fn().mockResolvedValue(undefined),
      },
      teamRepository: {
        getTeam: vi.fn().mockResolvedValue(null), // Will be overridden in test
        updateTeamStandings: vi.fn().mockResolvedValue(undefined),
        updateTeamBalance: vi.fn().mockResolvedValue(undefined),
        flush: vi.fn().mockResolvedValue(undefined),
      },
      playerRepository: {
        updatePlayerStats: vi.fn().mockResolvedValue(undefined),
      },
    },
  };

  beforeEach(async () => {
    process.env.FIXTURES_TABLE_NAME = 'fixturesTable';
    process.env.PLAYERS_TABLE_NAME = 'playersTable';
    process.env.TEAMS_TABLE_NAME = 'teamsTable';
    process.env.GET_TEAM_LAMBDA_ARN = 'getTeamLambdaArn';

    vi.resetAllMocks();

    // Mock team data for the repositories
    const team1Data = await team1Promise;
    const team2Data = await team2Promise;

    // Add MikroORM collection methods to the team data
    const mockTeam1 = {
      ...team1Data,
      players: {
        getItems: () => team1Data.players,
        map: (fn) => team1Data.players.map(fn),
      },
    };

    const mockTeam2 = {
      ...team2Data,
      players: {
        getItems: () => team2Data.players,
        map: (fn) => team2Data.players.map(fn),
      },
    };

    // Set up teamRepository.getTeam to return the appropriate team data
    sqsEvent.context.repositories.teamRepository.getTeam
      .mockResolvedValueOnce(mockTeam1) // First call for home team
      .mockResolvedValueOnce(mockTeam2); // Second call for away team

    // Mock the Lambda prototype httpInvoke method using spyOn
    vi.spyOn(Lambda.prototype, 'httpInvoke')
      .mockResolvedValueOnce({
        statusCode: 200,
        body: JSON.stringify(team1Data),
      })
      .mockResolvedValueOnce({
        statusCode: 200,
        body: JSON.stringify(team2Data),
      });

    // Mock batchUpdate using spyOn
    vi.spyOn(DynamoDbService.prototype, 'batchUpdate').mockResolvedValue({
      successful: [],
      failed: [],
    });
  });

  /*it('should simulate fixtures', async () => {
    const result = await handler(sqsEvent, context);
    expect(result).toBeUndefined();

    // Verify Lambda was called for both home and away teams
    expect(Lambda.prototype.httpInvoke).toHaveBeenCalledTimes(2);
  });*/
});

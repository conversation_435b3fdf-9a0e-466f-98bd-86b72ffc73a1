/* eslint-disable jest/unbound-method */
// No need to mock database initializer as we're using middleware

// Now import everything else
import { beforeEach, describe, expect, it, MockInstance, vi } from 'vitest';

import { League } from '@/entities/League.js';
import { LeagueRules } from '@/entities/LeagueRules.ts';
import { LeagueProcessor } from '@/functions/league/logic/LeagueProcessorV2.js';
import {
  processPlayerAging,
  sendRetirementNotifications,
} from '@/functions/league/logic/PlayerRetirement.js';
import { handler } from '@/functions/league/processEndOfSeason.js';
import createSqsEvent from '@/testing/createSqsEvent.js';
import { LeagueFactory } from '@/testing/factories/leagueFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import {
  mockLeagueRepository,
  mockTeamRepository,
  resetAllRepositoryMocks,
} from '@/testing/mockRepositories.js';
import { Lambda } from '@/utils/lambda.js';
import { InvokeCommandInput } from '@aws-sdk/client-lambda';

// Mock LeagueProcessor
vi.mock('@/functions/league/logic/LeagueProcessorV2.js', () => ({
  LeagueProcessor: {
    sortTeamsInLeague: vi.fn(),
    processPromotionsAndRelegations: vi.fn(),
    getTeamStandings: vi.fn(),
  },
}));

// Mock PlayerRetirement
vi.mock('@/functions/league/logic/PlayerRetirement.js', () => ({
  processPlayerAging: vi.fn(),
  sendRetirementNotifications: vi.fn(),
}));

vi.mock('@/services/sqs/sqs.js');

describe('Process End of Season', () => {
  const context: any = {};
  let lambdaInvokeSpy: MockInstance<
    (
      payload: string | undefined,
      arn: string,
      additionalCmdInput?: Partial<InvokeCommandInput>
    ) => Promise<void>
  >;

  beforeEach(() => {
    vi.clearAllMocks();
    resetAllRepositoryMocks();

    lambdaInvokeSpy = vi.spyOn(Lambda.prototype, 'eventInvoke').mockResolvedValue(undefined);

    // Mock player retirement functions with default empty responses
    (processPlayerAging as any).mockResolvedValue({
      newlyRetiringPlayers: [],
      totalPlayersAged: 0,
      removedPlayers: [],
    });
    (sendRetirementNotifications as any).mockResolvedValue(undefined);
  });

  it('should process league movements correctly', async () => {
    const team1 = TeamsFactory.build({
      teamId: 'team1',
      teamName: 'Team 1',
      points: 10,
      goalsFor: 20,
      goalsAgainst: 10,
      wins: 3,
      draws: 1,
      losses: 1,
      played: 5,
    });

    const team2 = TeamsFactory.build({
      teamId: 'team2',
      teamName: 'Team 2',
      points: 12,
      goalsFor: 15,
      goalsAgainst: 5,
      wins: 4,
      draws: 0,
      losses: 1,
      played: 5,
    });

    const leagues: League[] = [
      LeagueFactory.build({
        tier: 1,
        leagueRules: {
          teamCount: 15,
          promotionSpots: 0,
          relegationSpots: 1,
          minimumPrize: 1000,
          maximumPrize: 5000,
        } as LeagueRules,
        teams: {
          getItems: () => [team1],
          length: 1,
          [0]: team1,
        } as any,
      }),
      LeagueFactory.build({
        id: 'league2',
        tier: 2,
        name: 'League 2',
        leagueRules: {
          teamCount: 15,
          promotionSpots: 1,
          relegationSpots: 0,
          minimumPrize: 500,
          maximumPrize: 2500,
        } as any,
        teams: {
          getItems: () => [team2],
          length: 1,
          [0]: team2,
        } as any,
      }),
    ];

    const mockMovements = [
      { teamId: 'team1', fromLeagueId: 'league1', toLeagueId: 'league2' },
      { teamId: 'team2', fromLeagueId: 'league2', toLeagueId: 'league1' },
    ];

    // Mock repository methods
    mockLeagueRepository.getLeaguesByGameworld.mockResolvedValue(leagues);
    mockTeamRepository.updateTeamLeagues.mockResolvedValue(void 0);

    // Mock LeagueProcessor methods
    vi.spyOn(LeagueProcessor, 'sortTeamsInLeague').mockImplementation((t) => t);
    vi.spyOn(LeagueProcessor, 'processPromotionsAndRelegations').mockReturnValue(mockMovements);
    vi.spyOn(LeagueProcessor, 'getTeamStandings').mockReturnValue({
      teamName: 'Team 1',
      points: 10,
      goalDiff: 10,
      goalsFor: 20,
      wins: 3,
    });

    const event = createSqsEvent([
      {
        body: {
          gameworldId: 'test-gameworld',
        },
      },
    ]) as any;
    await handler(event, context);

    // Verify repository calls
    expect(mockLeagueRepository.getLeaguesByGameworld).toHaveBeenCalledWith('test-gameworld', true);
    expect(mockTeamRepository.updateTeamLeagues).toHaveBeenCalledWith(
      expect.arrayContaining([team1, team2]),
      mockMovements,
      'test-gameworld'
    );

    // Verify LeagueProcessor calls
    expect(LeagueProcessor.sortTeamsInLeague).toHaveBeenCalled();
    expect(LeagueProcessor.processPromotionsAndRelegations).toHaveBeenCalledWith(
      expect.any(Map),
      leagues
    );

    expect(lambdaInvokeSpy).toHaveBeenCalledWith(
      JSON.stringify({
        gameworldId: leagues[0]!.gameworld.id,
        leagueId: leagues[0]!.id,
      }),
      process.env.GENERATE_FIXTURES_LAMBDA_ARN!
    );
    expect(lambdaInvokeSpy).toHaveBeenCalledWith(
      JSON.stringify({
        gameworldId: leagues[1]!.gameworld.id,
        leagueId: leagues[1]!.id,
      }),
      process.env.GENERATE_FIXTURES_LAMBDA_ARN!
    );
  });

  it('should handle empty leagues gracefully', async () => {
    // Mock repository methods with empty arrays
    mockLeagueRepository.getLeaguesByGameworld.mockResolvedValue([]);

    const event = createSqsEvent([
      {
        body: {
          gameworldId: 'test-gameworld',
        },
      },
    ]) as any;
    await handler(event, context);

    expect(mockTeamRepository.updateTeamLeagues).not.toHaveBeenCalled();
  });

  it('should handle leagues with no teams gracefully', async () => {
    // Mock repository methods with leagues but no teams
    const emptyLeagues: League[] = [
      LeagueFactory.build({
        id: 'league1',
        tier: 1,
        name: 'League 1',
        leagueRules: {
          teamCount: 15,
          promotionSpots: 0,
          relegationSpots: 1,
          minimumPrize: 1000,
          maximumPrize: 5000,
        } as any,
        teams: {
          getItems: () => [],
          length: 0,
        } as any,
      }),
    ];

    mockLeagueRepository.getLeaguesByGameworld.mockResolvedValue(emptyLeagues);

    const event = createSqsEvent([
      {
        body: {
          gameworldId: 'test-gameworld',
        },
      },
    ]) as any;
    await handler(event, context);

    expect(mockTeamRepository.updateTeamLeagues).not.toHaveBeenCalled();
  });

  it('should process player retirement correctly', async () => {
    const newlyRetiringPlayers = [
      { playerId: 'player1', firstName: 'John', surname: 'Doe', age: 37 },
      { playerId: 'player2', firstName: 'Jane', surname: 'Smith', age: 39 },
    ];

    const removedPlayers = [{ playerId: 'player3', firstName: 'Old', surname: 'Player', age: 38 }];

    // Clear previous mocks and set up specific mocks for this test
    vi.clearAllMocks();

    // Mock player aging to return retiring players
    (processPlayerAging as any).mockResolvedValue({
      newlyRetiringPlayers,
      totalPlayersAged: 150,
      removedPlayers,
    });

    // Mock other functions
    (sendRetirementNotifications as any).mockResolvedValue(undefined);

    // Re-mock the lambda function
    lambdaInvokeSpy = vi.spyOn(Lambda.prototype, 'eventInvoke').mockResolvedValue(undefined);

    const mockTeam = TeamsFactory.build({ teamId: 'team1' });
    const leagues = [
      LeagueFactory.build({
        id: 'league1',
        teams: {
          getItems: () => [mockTeam],
          length: 1,
        } as any,
      }),
    ];

    mockLeagueRepository.getLeaguesByGameworld.mockResolvedValue(leagues);
    (LeagueProcessor.sortTeamsInLeague as any).mockReturnValue([]);
    (LeagueProcessor.processPromotionsAndRelegations as any).mockReturnValue([]);

    const event = createSqsEvent([
      {
        body: {
          gameworldId: 'test-gameworld',
        },
      },
    ]) as any;
    await handler(event, context);

    // Verify player aging was called
    expect(processPlayerAging).toHaveBeenCalledWith('test-gameworld', expect.any(Object));

    // Verify retirement notifications were sent
    expect(sendRetirementNotifications).toHaveBeenCalledWith(
      newlyRetiringPlayers,
      expect.any(Object)
    );
  });

  it('should skip retirement notifications when no players are retiring', async () => {
    // Mock player aging to return no retiring players
    (processPlayerAging as any).mockResolvedValue({
      newlyRetiringPlayers: [],
      totalPlayersAged: 100,
      removedPlayers: [],
    });

    const leagues = [
      LeagueFactory.build({
        id: 'league1',
        teams: {
          getItems: () => [],
          length: 0,
        } as any,
      }),
    ];

    mockLeagueRepository.getLeaguesByGameworld.mockResolvedValue(leagues);
    (LeagueProcessor.sortTeamsInLeague as any).mockReturnValue([]);
    (LeagueProcessor.processPromotionsAndRelegations as any).mockReturnValue([]);

    const event = createSqsEvent([
      {
        body: {
          gameworldId: 'test-gameworld',
        },
      },
    ]) as any;
    await handler(event, context);

    // Verify player aging was called
    expect(processPlayerAging).toHaveBeenCalledWith('test-gameworld', expect.any(Object));

    // Verify retirement notifications were NOT sent
    expect(sendRetirementNotifications).not.toHaveBeenCalled();
  });
});

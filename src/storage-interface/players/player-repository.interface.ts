import { Player } from '@/entities/Player.js';
import { PlayerMatchHistory } from '@/entities/PlayerMatchHistory.js';
import { ScoutedPlayer } from '@/entities/ScoutedPlayer.js';
import { TransferListedPlayer } from '@/entities/TransferListedPlayer.js';
import { CurrentAttributes } from '@/model/player.js';
import { BaseRepository } from '@/storage-interface/base-repository.js';
import { Loaded, type Rel } from '@mikro-orm/core';

export type AttributeUpdate = {
  playerId: string;
  attribute: keyof CurrentAttributes;
  attributeIncrement: number;
};

export interface PlayerRepository extends BaseRepository<Player> {
  batchCreatePlayers(players: Player[]): Promise<void>;
  getPlayer(gameworldId: string, playerId: string): Promise<Player | null>;
  getPlayersByTeam(gameworldId: string, teamId: string): Promise<Player[]>;
  getPlayersByLeague(gameworldId: string, leagueId: string): Promise<Player[]>;
  getPlayersWithoutTeam(gameworldId: string): Promise<Player[]>;
  getPlayersByGameworld(gameworldId: string): Promise<Player[]>;
  updatePlayer(player: Player): Promise<void>;
  updatePlayerStats(updatedPlayers: Player[]): Promise<void>;
  assignPlayerToTeam(gameworldId: string, playerId: string, teamId: string): Promise<void>;
  removePlayerFromTeam(gameworldId: string, playerId: string): Promise<void>;
  removePlayer(gameworldId: string, playerId: string): Promise<void>;
  addPlayerMatchHistory(
    gameworldId: string,
    playerId: string,
    fixtureId: string,
    stats: PlayerMatchHistory
  ): Promise<void>;
  getPlayerMatchHistory(
    gameworldId: string,
    playerId: string
  ): Promise<Array<{ fixtureId: string; stats: PlayerMatchHistory }>>;

  batchCreateTransferListedPlayers(transferListedPlayers: TransferListedPlayer[]): Promise<void>;

  /**
   * Check if a player has been scouted by a specific team
   * @param gameworldId The gameworld ID
   * @param playerId The player ID
   * @param teamId The team ID
   * @returns True if the player has been scouted by the team, false otherwise
   */
  isPlayerScoutedByTeam(gameworldId: string, playerId: string, teamId: string): Promise<boolean>;

  /**
   * Get all players scouted by a specific team
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   * @param limit The maximum number of players to return
   * @param lastEvaluatedKey The last evaluated key for pagination
   * @returns Array of scouted players
   */
  getPlayersScoutedByTeam(
    gameworldId: string,
    teamId: string,
    limit?: number,
    lastEvaluatedKey?: string
  ): Promise<Loaded<ScoutedPlayer, 'player' | 'player.attributes'>[]>;

  /**
   * Get random players from a league
   * @param gameworldId The gameworld ID
   * @param leagueId The league ID
   * @param count The number of random players to return
   * @returns Array of random players from the league
   */
  getRandomPlayersFromLeague(
    gameworldId: string,
    leagueId: string,
    count: number
  ): Promise<Player[]>;

  createFromPK(playerId: string): Rel<Player>;

  updatePlayerAttributesBatch(updates: AttributeUpdate[]): Promise<void>;
}

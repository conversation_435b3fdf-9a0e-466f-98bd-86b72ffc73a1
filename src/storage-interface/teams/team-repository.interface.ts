import { AvailableTeam } from '@/entities/AvailableTeam.js';
import { Fixture } from '@/entities/Fixture.js';
import { Team } from '@/entities/Team.js';
import { TeamMovement } from '@/functions/league/logic/LeagueProcessorV2.js';
import { LeagueStandings } from '@/model/team.js';
import { BaseRepository } from '@/storage-interface/base-repository.js';
import type { Rel } from '@mikro-orm/core';

export interface TeamRepository extends BaseRepository<Team> {
  batchInsertTeams(teams: Team[]): Promise<void>;
  batchInsertAvailableTeams(teams: AvailableTeam[]): Promise<void>;
  getTeamsByGameworld(gameworldId: string, includePlayers?: boolean): Promise<Team[]>;
  getTeamsByLeague(leagueId: string, includePlayers?: boolean): Promise<Team[]>;
  updateTeamLeague(teamId: string, gameworldId: string, newLeagueId: string): Promise<void>;
  resetTeamStandings(teamId: string, gameworldId: string): Promise<void>;
  updateTeamLeagues(teams: Team[], movements: TeamMovement[], gameworldId: string): Promise<void>;
  getRandomAvailableTeam(): Promise<AvailableTeam | null>;
  deleteAvailableTeam(team: AvailableTeam): Promise<number>;
  updateTeamStandings(
    teamId: string,
    gameworldId: string,
    standings: LeagueStandings,
    flush?: boolean
  ): Promise<void>;
  getTeam(gameworldId: string, teamId: string, includePlayers: boolean): Promise<Team | null>;

  /**
   * Find multiple teams by their IDs
   * @param teamIds Array of team IDs to find
   * @param includePlayers Whether to include players in the response
   * @returns Array of teams matching the provided IDs
   */
  findByIds(teamIds: string[], includePlayers?: boolean): Promise<Team[]>;

  /**
   * Get all teams without a manager
   * @param includePlayers Whether to include players in the response
   */
  getTeamsWithoutManager(includePlayers?: boolean): Promise<Team[]>;

  /**
   * Update a team's balance
   * @param teamId The team ID
   * @param gameworldId The gameworld ID
   * @param amount The amount to add to the balance
   */
  updateTeamBalance(teamId: string, gameworldId: string, amount: number): Promise<void>;

  /**
   * Update a team's selection order
   * @param teamId The team ID
   * @param gameworldId The gameworld ID
   * @param selectionOrder The new selection order array of player IDs
   */
  updateTeamSelectionOrder(
    teamId: string,
    gameworldId: string,
    selectionOrder: string[]
  ): Promise<void>;

  getTeamAndNextMatch(
    gameworldId: string,
    teamId: string,
    includePlayers: boolean
  ): Promise<{ team: Team | null; nextFixture: Fixture | null }>;

  incrementTrainingLevel(team: Team, cost: number): Promise<void>;

  flush(): Promise<void>;

  createFromPK(teamId: string): Rel<Team>;
}

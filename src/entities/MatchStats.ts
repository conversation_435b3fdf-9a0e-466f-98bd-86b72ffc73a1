import { Embeddable, Property } from '@mikro-orm/core';
import { Scorer } from './types.js';

@Embeddable()
export class MatchStats {
  @Property({ type: 'int[]' })
  possession!: [number, number];

  @Property({ type: 'int[]' })
  shots!: [number, number];

  @Property({ type: 'int[]' })
  shotsOnTarget!: [number, number];

  @Property({ type: 'int[]' })
  corners!: [number, number];

  @Property({ type: 'int[]' })
  fouls!: [number, number];

  @Property({ type: 'int[]' })
  yellowCards!: [number, number];

  @Property({ type: 'int[]' })
  redCards!: [number, number];

  @Property({ type: 'int[]' })
  passes!: [number, number];

  @Property({ type: 'int[]' })
  passAccuracy!: [number, number];

  @Property({ type: 'int[]' })
  tackles!: [number, number];

  @Property({ type: 'int[]' })
  interceptions!: [number, number];

  @Property({ type: 'int[]' })
  score!: [number, number];

  @Property({ type: 'json', nullable: true })
  scorers?: Scorer[];
}

import {
  Cascade,
  Entity,
  ManyToOne,
  type Opt,
  PrimaryKey,
  PrimaryKeyProp,
  Property,
  Ref,
} from '@mikro-orm/core';
import { Fixture } from './Fixture.js';
import { Player } from './Player.js';

@Entity({ tableName: 'player_match_history' })
export class PlayerMatchHistory {
  [PrimaryKeyProp]?: 'fixtureId';

  @ManyToOne({
    entity: () => Player,
    fieldName: 'player_id',
    nullable: true,
    onDelete: 'set null',
  })
  player?: Ref<Player>;

  @PrimaryKey({ type: 'uuid' })
  fixtureId!: string;

  @ManyToOne({
    entity: () => Fixture,
    fieldName: 'fixture_fixture_id',
  })
  fixture!: Ref<Fixture>;

  @Property({ type: 'integer' })
  yellowCards: number & Opt = 0;

  @Property({ type: 'integer' })
  redCards: number & Opt = 0;

  @Property({ type: 'integer' })
  passesCompleted: number & Opt = 0;

  @Property({ type: 'integer' })
  passesAttempted: number & Opt = 0;

  @Property({ type: 'integer' })
  successfulBallCarries: number & Opt = 0;

  @Property({ type: 'integer' })
  ballCarriesAttempted: number & Opt = 0;

  @Property({ type: 'integer' })
  shots: number & Opt = 0;

  @Property({ type: 'integer' })
  shotsOnTarget: number & Opt = 0;

  @Property({ type: 'integer' })
  goals: number & Opt = 0;

  @Property({ type: 'integer' })
  saves: number & Opt = 0;

  @Property({ type: 'integer' })
  tackles: number & Opt = 0;

  @Property({ type: 'integer' })
  fouls: number & Opt = 0;
}

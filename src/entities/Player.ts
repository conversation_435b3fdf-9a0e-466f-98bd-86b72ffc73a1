import {
  Collection,
  Entity,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryKey,
  PrimaryKeyProp,
  Property,
  type Rel,
  Unique,
} from '@mikro-orm/core';
import { PlayerAttributes } from './PlayerAttributes.js';
import { PlayerMatchHistory } from './PlayerMatchHistory.js';
import { PlayerOverallStats } from './PlayerOverallStats.js';
import { Team } from './Team.js';

@Entity({ tableName: 'players' })
@Unique({ name: 'players_gameworld_id_player_id_key', properties: ['gameworldId', 'playerId'] })
export class Player {
  [PrimaryKeyProp]?: 'playerId';

  @PrimaryKey({ type: 'uuid' })
  playerId!: string;

  @Property({ type: 'uuid' })
  gameworldId!: string;

  @ManyToOne({ entity: () => Team, fieldName: 'team_id', nullable: true })
  team?: Rel<Team>;

  @Property()
  age!: number;

  @Property({ type: 'bigint' })
  seed!: number;

  @Property()
  firstName!: string;

  @Property()
  surname!: string;

  @Property({ type: 'numeric', precision: 15, scale: 2 })
  value!: number;

  @Property({ type: 'int' })
  energy!: number; // 0 - 100 value

  @Property({ type: 'bigint' })
  lastMatchPlayed!: number;

  @Property({ nullable: true, type: 'bigint' })
  injuredUntil?: number;

  @Property()
  suspendedForGames!: number;

  @OneToOne(() => PlayerAttributes, (attributes) => attributes.player)
  attributes!: Rel<PlayerAttributes>;

  @OneToOne(() => PlayerOverallStats, (overallStats) => overallStats.player, { nullable: true })
  overallStats?: Rel<PlayerOverallStats>;

  @OneToMany(() => PlayerMatchHistory, (matchHistory) => matchHistory.player)
  matchHistory = new Collection<PlayerMatchHistory>(this);

  @Property({ type: 'boolean', default: false })
  isTransferListed: boolean = false;

  @Property({ type: 'boolean', default: false })
  retiringAtEndOfSeason: boolean = false;
}

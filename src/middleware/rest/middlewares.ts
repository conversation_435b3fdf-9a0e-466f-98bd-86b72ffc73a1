import { databaseMiddleware } from '@/middleware/database/index.js';
import { EventMiddlewareObj } from '@/middleware/event/types.js';
import { conditionalJsonBodyParser } from '@/middleware/rest/conditionalBodyParser.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import { injectLambdaContext } from '@aws-lambda-powertools/logger/middleware';
import { captureLambdaHandler } from '@aws-lambda-powertools/tracer/middleware';
import cors from '@middy/http-cors';
import httpEventNormalizer from '@middy/http-event-normalizer';
import httpHeaderNormalizer from '@middy/http-header-normalizer';
import httpsecurityHeaders from '@middy/http-security-headers';
import type { HttpMiddlewareObj } from './types.js';

/**
 * The default middleware that will be used for all lambda handlers.
 */
export const defaultHttpMiddlewares = (schema?: object, injectRepositories = true) => {
  const middlewares = [
    httpEventNormalizer(),
    httpHeaderNormalizer(),
    injectLambdaContext(logger, {
      logEvent: true,
    }),
    captureLambdaHandler(tracer, {
      captureResponse: true,
    }),
    cors({
      methods: 'GET,POST,PUT,DELETE,OPTIONS,PATCH',
    }),
    httpsecurityHeaders(),
  ] as EventMiddlewareObj[];

  if (injectRepositories) {
    middlewares.push(databaseMiddleware());
  }

  if (!schema) {
    middlewares.splice(2, 0, conditionalJsonBodyParser() as HttpMiddlewareObj);
  }

  return middlewares;
};

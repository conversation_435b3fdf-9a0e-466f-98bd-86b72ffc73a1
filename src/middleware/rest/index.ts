import middy from '@middy/core';
import validator from '@middy/validator';
import { Ajv } from 'ajv';
import * as ajvFormats from 'ajv-formats';

import { customErrorHandler } from '@/middleware/rest/customErrorHandler.js';
import { logger } from '@/utils/logger.js';
import { conditionalJsonBodyParser } from './conditionalBodyParser.js';
import { defaultHttpMiddlewares } from './middlewares.js';
import { HttpHandler, HttpMiddifyOptions } from './types.js';

const formats = ajvFormats.default;

/**
 * @param handler - Event Lambda handler
 * @param options - Middleware options
 * @returns
 */
export const httpMiddify = <TBody, TPathParameters, TQueryStringParameters>(
  handler: HttpHandler<TBody, TPathParameters, TQueryStringParameters>,
  { schema, validatorOptions, validatorCallback, injectRepositories }: HttpMiddifyOptions
) => {
  const wrapped = middy(handler);

  if (schema) {
    logger.debug('Schema specified. Adding json body parser and validator middlewares');

    const ajv = new Ajv({
      strict: true,
      coerceTypes: 'array',
      allErrors: true,
      useDefaults: 'empty',
      messages: true,
      ...validatorOptions,
    });
    formats(ajv);

    // can use this to call ajv.addSchema(someReferencedSchema) if needed
    if (validatorCallback) {
      validatorCallback(ajv);
    }

    wrapped.use(conditionalJsonBodyParser()).use(
      validator({
        eventSchema: ajv.compile(schema),
      })
    );
  }

  wrapped.use(defaultHttpMiddlewares(schema, injectRepositories));
  wrapped.use(customErrorHandler());

  return wrapped;
};

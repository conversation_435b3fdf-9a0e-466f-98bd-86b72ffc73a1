import { Migration } from '@mikro-orm/migrations';

export class Migration20250510213131 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `alter table "players" add column "is_transfer_listed" boolean not null default false;`
    );

    this.addSql(`
      UPDATE "players"
      SET "is_transfer_listed" = true
      WHERE "player_id" IN (SELECT "player_id" FROM "transfer_list");
    `);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "players" drop column "is_transfer_listed";`);
  }
}

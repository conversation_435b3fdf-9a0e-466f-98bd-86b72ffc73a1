import { Migration } from '@mikro-orm/migrations';

export class Migration20250413084714_add_transfer_list extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `create table "transfer_list" ("player_id" uuid not null, "gameworld_id" uuid not null, "team_id" uuid null, "league_id" uuid null, "age" int not null, "seed" int not null, "first_name" varchar(255) not null, "surname" varchar(255) not null, "value" int not null, "auction_start_price" int not null, "auction_end_time" bigint not null, "bid_history" jsonb not null, "attributes" jsonb not null, constraint "transfer_list_pkey" primary key ("player_id"));`
    );
    this.addSql(
      `alter table "transfer_list" add constraint "transfer_list_gameworld_id_player_id_key" unique ("gameworld_id", "player_id");`
    );

    this.addSql(
      `alter table "transfer_list" add constraint "transfer_list_team_id_foreign" foreign key ("team_id") references "team" ("team_id") on update cascade on delete set null;`
    );
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "transfer_list" cascade;`);
  }
}

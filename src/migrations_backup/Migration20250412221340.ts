import { Migration } from '@mikro-orm/migrations';

export class Migration20250412221340 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `create table "fixture" ("fixture_id" uuid not null, "gameworld_id" uuid not null, "league_id" uuid not null, "home_team_team_id" uuid not null, "away_team_team_id" uuid not null, "date" bigint not null, "stats_possession" text[] null, "stats_shots" text[] null, "stats_shots_on_target" text[] null, "stats_corners" text[] null, "stats_fouls" text[] null, "stats_yellow_cards" text[] null, "stats_red_cards" text[] null, "stats_passes" text[] null, "stats_pass_accuracy" text[] null, "stats_tackles" text[] null, "stats_interceptions" text[] null, "stats_score" text[] null, "stats_scorers" jsonb null, "events" jsonb null, "played" boolean not null default false, "simulated_at" bigint null, "seed" int null, constraint "fixture_pkey" primary key ("fixture_id"));`
    );
    this.addSql(
      `alter table "fixture" add constraint "fixture_gameworld_id_league_id_fixture_id_unique" unique ("gameworld_id", "league_id", "fixture_id");`
    );

    this.addSql(
      `create table "available_team" ("id" uuid not null, "gameworld_id" uuid not null, "team_team_id" uuid not null, constraint "available_team_pkey" primary key ("id"));`
    );
    this.addSql(
      `alter table "available_team" add constraint "available_team_team_team_id_unique" unique ("team_team_id");`
    );
    this.addSql(
      `alter table "available_team" add constraint "available_team_gameworld_id_team_team_id_unique" unique ("gameworld_id", "team_team_id");`
    );

    this.addSql(
      `alter table "fixture" add constraint "fixture_league_id_foreign" foreign key ("league_id") references "league" ("id") on update cascade;`
    );
    this.addSql(
      `alter table "fixture" add constraint "fixture_home_team_team_id_foreign" foreign key ("home_team_team_id") references "team" ("team_id") on update cascade;`
    );
    this.addSql(
      `alter table "fixture" add constraint "fixture_away_team_team_id_foreign" foreign key ("away_team_team_id") references "team" ("team_id") on update cascade;`
    );

    this.addSql(
      `alter table "available_team" add constraint "available_team_team_team_id_foreign" foreign key ("team_team_id") references "team" ("team_id") on update cascade;`
    );
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "fixture" cascade;`);

    this.addSql(`drop table if exists "available_team" cascade;`);
  }
}

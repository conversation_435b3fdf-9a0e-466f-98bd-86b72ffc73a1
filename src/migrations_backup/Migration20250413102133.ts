import { Migration } from '@mikro-orm/migrations';

export class Migration20250413102133 extends Migration {
  override async up(): Promise<void> {
    this.addSql(`alter table "players" drop constraint "players_team_id_foreign";`);

    this.addSql(`alter table "transfer_list" drop constraint "transfer_list_team_id_foreign";`);

    this.addSql(`alter table "players" alter column "team_id" drop default;`);
    this.addSql(
      `alter table "players" alter column "team_id" type uuid using ("team_id"::text::uuid);`
    );
    this.addSql(`alter table "players" alter column "team_id" drop not null;`);
    this.addSql(`alter table "players" alter column "league_id" drop default;`);
    this.addSql(
      `alter table "players" alter column "league_id" type uuid using ("league_id"::text::uuid);`
    );
    this.addSql(`alter table "players" alter column "league_id" drop not null;`);
    this.addSql(
      `alter table "players" add constraint "players_team_id_foreign" foreign key ("team_id") references "team" ("team_id") on update cascade on delete set null;`
    );

    this.addSql(
      `alter table "transfer_list" drop column "team_id", drop column "league_id", drop column "age", drop column "seed", drop column "first_name", drop column "surname", drop column "value", drop column "attributes";`
    );

    this.addSql(`alter table "transfer_list" add column "player_player_id" uuid not null;`);
    this.addSql(
      `alter table "transfer_list" add constraint "transfer_list_player_player_id_foreign" foreign key ("player_player_id") references "players" ("player_id") on update cascade;`
    );
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "players" drop constraint "players_team_id_foreign";`);

    this.addSql(
      `alter table "transfer_list" drop constraint "transfer_list_player_player_id_foreign";`
    );

    this.addSql(`alter table "players" alter column "team_id" drop default;`);
    this.addSql(
      `alter table "players" alter column "team_id" type uuid using ("team_id"::text::uuid);`
    );
    this.addSql(`alter table "players" alter column "team_id" set not null;`);
    this.addSql(`alter table "players" alter column "league_id" drop default;`);
    this.addSql(
      `alter table "players" alter column "league_id" type uuid using ("league_id"::text::uuid);`
    );
    this.addSql(`alter table "players" alter column "league_id" set not null;`);
    this.addSql(
      `alter table "players" add constraint "players_team_id_foreign" foreign key ("team_id") references "team" ("team_id") on update cascade;`
    );

    this.addSql(`alter table "transfer_list" drop column "player_player_id";`);

    this.addSql(
      `alter table "transfer_list" add column "team_id" uuid null, add column "league_id" uuid null, add column "age" int not null, add column "seed" int not null, add column "first_name" varchar(255) not null, add column "surname" varchar(255) not null, add column "value" int not null, add column "attributes" jsonb not null;`
    );
    this.addSql(
      `alter table "transfer_list" add constraint "transfer_list_team_id_foreign" foreign key ("team_id") references "team" ("team_id") on update cascade on delete set null;`
    );
  }
}

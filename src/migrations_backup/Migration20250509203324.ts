import { Migration } from '@mikro-orm/migrations';

export class Migration20250509203324 extends Migration {
  override async up(): Promise<void> {
    this.addSql(`alter table "transfer_request" drop constraint "transfer_request_id_unique";`);
    this.addSql(`alter table "transfer_request" drop constraint "transfer_request_pkey";`);

    this.addSql(
      `alter table "transfer_request" add constraint "transfer_request_player_id_buyerTeam_unique" unique ("player_id", "buyerTeam");`
    );
    this.addSql(
      `alter table "transfer_request" add constraint "transfer_request_pkey" primary key ("id");`
    );
  }

  override async down(): Promise<void> {
    this.addSql(
      `alter table "transfer_request" drop constraint "transfer_request_player_id_buyerTeam_unique";`
    );
    this.addSql(`alter table "transfer_request" drop constraint "transfer_request_pkey";`);

    this.addSql(
      `alter table "transfer_request" add constraint "transfer_request_id_unique" unique ("id");`
    );
    this.addSql(
      `alter table "transfer_request" add constraint "transfer_request_pkey" primary key ("id", "buyerTeam", "sellerTeam");`
    );
  }
}

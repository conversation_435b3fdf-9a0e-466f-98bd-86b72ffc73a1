import { Migration } from '@mikro-orm/migrations';

export class Migration20250413110428 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `alter table "players" add constraint "players_league_id_foreign" foreign key ("league_id") references "league" ("id") on update cascade on delete set null;`
    );
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "players" drop constraint "players_league_id_foreign";`);
  }
}

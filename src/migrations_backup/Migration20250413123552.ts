import { Migration } from '@mikro-orm/migrations';

export class Migration20250413123552 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `alter table "players" alter column "value" type numeric(15,2) using ("value"::numeric(15,2));`
    );

    this.addSql(
      `alter table "transfer_list" alter column "auction_start_price" type numeric(15,2) using ("auction_start_price"::numeric(15,2));`
    );
  }

  override async down(): Promise<void> {
    this.addSql(
      `alter table "players" alter column "value" type numeric(10,2) using ("value"::numeric(10,2));`
    );

    this.addSql(
      `alter table "transfer_list" alter column "auction_start_price" type int using ("auction_start_price"::int);`
    );
  }
}

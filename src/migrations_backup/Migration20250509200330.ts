import { Migration } from '@mikro-orm/migrations';

export class Migration20250509200330 extends Migration {
  override async up(): Promise<void> {
    // First add the column as nullable
    this.addSql(`alter table "transfer_list" add column "auction_current_price" numeric(15,2);`);

    // Update existing records to set auction_current_price equal to auction_start_price
    this.addSql(`update "transfer_list" set "auction_current_price" = "auction_start_price";`);

    // Now make the column NOT NULL
    this.addSql(`alter table "transfer_list" alter column "auction_current_price" set not null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "transfer_list" drop column "auction_current_price";`);
  }
}

import { Migration } from '@mikro-orm/migrations';

export class Migration20250513123251 extends Migration {
  override async up(): Promise<void> {
    // Enable UUID extension if not already enabled
    this.addSql('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";');
    this.addSql(
      `alter table "transfer_list" drop constraint "transfer_list_player_player_id_foreign";`
    );

    this.addSql(
      `alter table "bid_history" drop constraint "bid_history_transfer_listed_player_player_id_tra_ee32c_foreign";`
    );

    this.addSql(`alter table "transfer_list" drop constraint "transfer_list_pkey";`);
    this.addSql(`alter table "transfer_list" drop column "player_player_id";`);

    // First add the column as nullable
    this.addSql(`alter table "transfer_list" add column "id" uuid;`);

    // Generate UUIDs for existing rows
    this.addSql(`UPDATE "transfer_list" SET "id" = uuid_generate_v4();`);

    // Now make the column not null
    this.addSql(`alter table "transfer_list" alter column "id" set not null;`);
    this.addSql(
      `alter table "transfer_list" add constraint "transfer_list_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on update cascade;`
    );
    this.addSql(
      `alter table "transfer_list" add constraint "transfer_list_pkey" primary key ("id", "gameworld_id");`
    );

    this.addSql(
      `alter table "bid_history" drop constraint "bid_history_transfer_listed_player_player_id_tran_6504e_unique";`
    );

    // First rename the column
    this.addSql(
      `alter table "bid_history" rename column "transfer_listed_player_player_id" to "transfer_listed_player_id";`
    );

    // Now update the bid_history table to match the new IDs in transfer_list
    this.addSql(`
      UPDATE "bid_history" b
      SET "transfer_listed_player_id" = t."id"
      FROM "transfer_list" t
      WHERE b."transfer_listed_player_id" = t."player_id" AND b."transfer_listed_player_gameworld_id" = t."gameworld_id";
    `);
    this.addSql(
      `alter table "bid_history" add constraint "bid_history_transfer_listed_player_id_transfer_l_93d1a_foreign" foreign key ("transfer_listed_player_id", "transfer_listed_player_gameworld_id") references "transfer_list" ("id", "gameworld_id") on update cascade;`
    );
    this.addSql(
      `alter table "bid_history" add constraint "bid_history_transfer_listed_player_id_transfer_li_423c1_unique" unique ("transfer_listed_player_id", "transfer_listed_player_gameworld_id", "team_team_id", "bid_time");`
    );
  }

  override async down(): Promise<void> {
    this.addSql(
      `alter table "transfer_list" drop constraint if exists "transfer_list_player_id_foreign";`
    );

    this.addSql(
      `alter table "bid_history" drop constraint if exists "bid_history_transfer_listed_player_id_transfer_l_93d1a_foreign";`
    );

    this.addSql(`alter table "transfer_list" drop constraint if exists "transfer_list_pkey";`);
    this.addSql(`alter table "transfer_list" drop column if exists "id";`);

    // First add the column as nullable
    this.addSql(`alter table "transfer_list" add column "player_player_id" uuid;`);

    // Update with player_id values
    this.addSql(`UPDATE "transfer_list" SET "player_player_id" = "player_id";`);

    // Make it not null
    this.addSql(`alter table "transfer_list" alter column "player_player_id" set not null;`);

    this.addSql(
      `alter table "transfer_list" add constraint "transfer_list_player_player_id_foreign" foreign key ("player_player_id") references "players" ("player_id") on update cascade;`
    );
    this.addSql(
      `alter table "transfer_list" add constraint "transfer_list_pkey" primary key ("player_id", "gameworld_id");`
    );

    this.addSql(
      `alter table "bid_history" drop constraint if exists "bid_history_transfer_listed_player_id_transfer_li_423c1_unique";`
    );

    // First update the bid_history table to use player_id instead of id
    this.addSql(`
      UPDATE "bid_history" b
      SET "transfer_listed_player_id" = t."player_id"
      FROM "transfer_list" t
      WHERE b."transfer_listed_player_id" = t."id" AND b."transfer_listed_player_gameworld_id" = t."gameworld_id";
    `);

    // Now rename the column
    this.addSql(
      `alter table "bid_history" rename column "transfer_listed_player_id" to "transfer_listed_player_player_id";`
    );
    this.addSql(
      `alter table "bid_history" add constraint "bid_history_transfer_listed_player_player_id_tra_ee32c_foreign" foreign key ("transfer_listed_player_player_id", "transfer_listed_player_gameworld_id") references "transfer_list" ("player_id", "gameworld_id") on update cascade;`
    );
    this.addSql(
      `alter table "bid_history" add constraint "bid_history_transfer_listed_player_player_id_tran_6504e_unique" unique ("transfer_listed_player_player_id", "transfer_listed_player_gameworld_id", "team_team_id", "bid_time");`
    );
  }
}

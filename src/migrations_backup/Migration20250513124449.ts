import { Migration } from '@mikro-orm/migrations';

export class Migration20250513124449 extends Migration {
  override async up(): Promise<void> {
    // Enable UUID extension if not already enabled
    this.addSql('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";');

    // Drop existing tables if they exist
    this.addSql('DROP TABLE IF EXISTS "bid_history" CASCADE;');
    this.addSql('DROP TABLE IF EXISTS "transfer_list" CASCADE;');

    // Create the transfer_list table from scratch
    this.addSql(`
      CREATE TABLE "transfer_list" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "player_id" uuid NOT NULL,
        "gameworld_id" uuid NOT NULL,
        "player_player_id" uuid NOT NULL,
        "auction_start_price" numeric(15,2) NOT NULL,
        "auction_current_price" numeric(15,2) NOT NULL,
        "auction_end_time" bigint NOT NULL,
        "auction_listing_counter" int NOT NULL DEFAULT 0,
        "created_at" bigint NOT NULL,
        CONSTRAINT "transfer_list_pkey" PRIMARY KEY ("id"),
        CONSTRAINT "transfer_list_player_player_id_foreign" FOREIGN KEY ("player_player_id") REFERENCES "players" ("player_id") ON UPDATE CASCADE,
        CONSTRAINT "transfer_list_player_player_id_gameworld_id_unique" UNIQUE ("player_player_id", "gameworld_id")
      );
    `);

    // Create indexes for transfer_list
    this.addSql('CREATE INDEX "transfer_list_player_id_index" ON "transfer_list" ("player_id");');
    this.addSql(
      'CREATE INDEX "transfer_list_gameworld_id_index" ON "transfer_list" ("gameworld_id");'
    );
    this.addSql(
      'CREATE INDEX "transfer_list_auction_end_time_index" ON "transfer_list" ("auction_end_time");'
    );

    // Create the bid_history table from scratch
    this.addSql(`
      CREATE TABLE "bid_history" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "transfer_listing_id" uuid NOT NULL,
        "team_id" uuid NOT NULL,
        "maximum_bid" numeric(15,2) NOT NULL,
        "bid_time" bigint NOT NULL,
        "is_winning_bid" boolean NOT NULL DEFAULT false,
        CONSTRAINT "bid_history_pkey" PRIMARY KEY ("id"),
        CONSTRAINT "bid_history_transfer_listing_id_foreign" FOREIGN KEY ("transfer_listing_id") REFERENCES "transfer_list" ("id") ON UPDATE CASCADE ON DELETE CASCADE,
        CONSTRAINT "bid_history_team_id_foreign" FOREIGN KEY ("team_id") REFERENCES "team" ("team_id") ON UPDATE CASCADE,
        CONSTRAINT "bid_history_transfer_listing_id_team_id_unique" UNIQUE ("transfer_listing_id", "team_id")
      );
    `);

    // Create indexes for bid_history
    this.addSql(
      'CREATE INDEX "bid_history_transfer_listing_id_index" ON "bid_history" ("transfer_listing_id");'
    );
    this.addSql('CREATE INDEX "bid_history_team_id_index" ON "bid_history" ("team_id");');
  }

  override async down(): Promise<void> {
    // Drop the tables in reverse order
    this.addSql('DROP TABLE IF EXISTS "bid_history" CASCADE;');
    this.addSql('DROP TABLE IF EXISTS "transfer_list" CASCADE;');
  }
}

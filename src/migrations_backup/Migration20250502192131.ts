import { Migration } from '@mikro-orm/migrations';

export class Migration20250502192131 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `alter table "players" alter column "injured_until" type bigint using ("injured_until"::bigint);`
    );
  }

  override async down(): Promise<void> {
    this.addSql(
      `alter table "players" alter column "injured_until" type int using ("injured_until"::int);`
    );
  }
}

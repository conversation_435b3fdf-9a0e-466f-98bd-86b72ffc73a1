import { Migration } from '@mikro-orm/migrations';

export class Migration20250505195429 extends Migration {
  override async up(): Promise<void> {
    this.addSql(`alter table "league" alter column "id" drop default;`);
    this.addSql(`alter table "league" alter column "id" type uuid using ("id"::text::uuid);`);
    this.addSql(`alter table "league" alter column "id" set default uuid_generate_v4();`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "league" alter column "id" drop default;`);
    this.addSql(`alter table "league" alter column "id" drop default;`);
    this.addSql(`alter table "league" alter column "id" type uuid using ("id"::text::uuid);`);
  }
}

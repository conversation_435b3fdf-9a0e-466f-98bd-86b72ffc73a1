import { Migration } from '@mikro-orm/migrations';

export class Migration20250508200307 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `create table "transfer_request" ("id" uuid not null default uuid_generate_v4(), "buyerTeam" uuid not null, "sellerTeam" uuid not null, "date" bigint not null, "player_id" uuid not null, "value" bigint not null, "counter_offer_time" bigint not null default 0, "counter_offer_value" bigint not null default 0, constraint "transfer_request_pkey" primary key ("id", "buyerTeam", "sellerTeam"));`
    );
    this.addSql(
      `alter table "transfer_request" add constraint "transfer_request_id_unique" unique ("id");`
    );

    this.addSql(
      `alter table "transfer_request" add constraint "transfer_request_buyerTeam_foreign" foreign key ("buyerTeam") references "team" ("team_id") on update cascade;`
    );
    this.addSql(
      `alter table "transfer_request" add constraint "transfer_request_sellerTeam_foreign" foreign key ("sellerTeam") references "team" ("team_id") on update cascade;`
    );
    this.addSql(
      `alter table "transfer_request" add constraint "transfer_request_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on update cascade;`
    );
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "transfer_request" cascade;`);
  }
}

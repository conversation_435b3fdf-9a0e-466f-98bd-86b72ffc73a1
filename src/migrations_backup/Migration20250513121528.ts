import { Migration } from '@mikro-orm/migrations';

export class Migration20250513121528 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `alter table "bid_history" drop constraint if exists "bid_history_transfer_listed_player_player_id_foreign";`
    );

    this.addSql(
      `alter table "transfer_list" drop constraint if exists "transfer_list_gameworld_id_player_id_key";`
    );
    this.addSql(`alter table "transfer_list" drop constraint if exists "transfer_list_pkey";`);

    this.addSql(
      `alter table "transfer_list" add constraint "transfer_list_pkey" primary key ("player_id", "gameworld_id");`
    );

    // Check if the constraint exists before trying to drop it
    this.addSql(`DO $$
    BEGIN
      IF EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = 'bid_history_transfer_listed_player_player_id_team_de4c1_unique'
      ) THEN
        ALTER TABLE "bid_history" DROP CONSTRAINT "bid_history_transfer_listed_player_player_id_team_de4c1_unique";
      ELSIF EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = 'bid_history_player_team_time_unique'
      ) THEN
        ALTER TABLE "bid_history" DROP CONSTRAINT "bid_history_player_team_time_unique";
      END IF;
    END
    $$;`);

    // First add the column as nullable
    this.addSql(`alter table "bid_history" add column "transfer_listed_player_gameworld_id" uuid;`);

    // Update existing rows with gameworld_id from transfer_list table
    this.addSql(`
      UPDATE "bid_history" b
      SET "transfer_listed_player_gameworld_id" = t."gameworld_id"
      FROM "transfer_list" t
      WHERE b."transfer_listed_player_player_id" = t."player_id";
    `);

    // Now make the column not null
    this.addSql(
      `alter table "bid_history" alter column "transfer_listed_player_gameworld_id" set not null;`
    );
    this.addSql(
      `alter table "bid_history" add constraint "bid_history_transfer_listed_player_player_id_tra_ee32c_foreign" foreign key ("transfer_listed_player_player_id", "transfer_listed_player_gameworld_id") references "transfer_list" ("player_id", "gameworld_id") on update cascade;`
    );
    this.addSql(
      `alter table "bid_history" add constraint "bid_history_transfer_listed_player_player_id_tran_6504e_unique" unique ("transfer_listed_player_player_id", "transfer_listed_player_gameworld_id", "team_team_id", "bid_time");`
    );
  }

  override async down(): Promise<void> {
    this.addSql(
      `alter table "bid_history" drop constraint if exists "bid_history_transfer_listed_player_player_id_tra_ee32c_foreign";`
    );

    this.addSql(`alter table "transfer_list" drop constraint if exists "transfer_list_pkey";`);

    this.addSql(
      `alter table "transfer_list" add constraint "transfer_list_gameworld_id_player_id_key" unique ("gameworld_id", "player_id");`
    );
    this.addSql(
      `alter table "transfer_list" add constraint "transfer_list_pkey" primary key ("player_id");`
    );

    this.addSql(
      `alter table "bid_history" drop constraint if exists "bid_history_transfer_listed_player_player_id_tran_6504e_unique";`
    );
    this.addSql(`alter table "bid_history" drop column "transfer_listed_player_gameworld_id";`);

    this.addSql(
      `alter table "bid_history" add constraint "bid_history_transfer_listed_player_player_id_foreign" foreign key ("transfer_listed_player_player_id") references "transfer_list" ("player_id") on update cascade;`
    );
    this.addSql(
      `alter table "bid_history" add constraint "bid_history_player_team_time_unique" unique ("transfer_listed_player_player_id", "team_team_id", "bid_time");`
    );
  }
}

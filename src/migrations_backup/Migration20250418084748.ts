import { Migration } from '@mikro-orm/migrations';

export class Migration20250418084748 extends Migration {
  override async up(): Promise<void> {
    // First, create the uuid-ossp extension if it doesn't exist
    this.addSql(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`);

    // Then create the table using the uuid_generate_v4() function
    this.addSql(
      `create table "scouting_requests" ("request_id" uuid not null default uuid_generate_v4(), "gameworld_id" uuid not null, "team_team_id" uuid not null, "type" text check ("type" in ('player', 'team', 'league')) not null, "target_id" varchar(255) not null, "process_after" bigint not null, "processed_at" bigint null, "processed" boolean not null default false, "created_at" timestamptz not null default now(), constraint "scouting_requests_pkey" primary key ("request_id"));`
    );
    this.addSql(
      `alter table "scouting_requests" add constraint "scouting_requests_request_id_unique" unique ("request_id");`
    );

    this.addSql(
      `alter table "scouting_requests" add constraint "scouting_requests_team_team_id_foreign" foreign key ("team_team_id") references "team" ("team_id") on update cascade;`
    );
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "scouting_requests" cascade;`);

    // Note: We're not dropping the uuid-ossp extension as it might be used by other tables
    // If you want to drop it, uncomment the line below
    // this.addSql(`DROP EXTENSION IF EXISTS "uuid-ossp";`);
  }
}

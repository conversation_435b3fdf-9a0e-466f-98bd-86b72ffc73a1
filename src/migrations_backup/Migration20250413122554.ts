import { Migration } from '@mikro-orm/migrations';

export class Migration20250413122554 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `alter table "players" alter column "value" type numeric(10,2) using ("value"::numeric(10,2));`
    );
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "players" alter column "value" type int using ("value"::int);`);
  }
}

import { Migration } from '@mikro-orm/migrations';

export class Migration20250412174221 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `create table "league" ("id" uuid not null, "gameworld_id" uuid not null, "name" varchar(100) not null, "tier" int not null, "parent_league_id" uuid null, constraint "league_pkey" primary key ("id"));`
    );
    this.addSql(`create index "idx_leagues_gameworld" on "league" ("gameworld_id");`);
    this.addSql(`create index "idx_leagues_tier" on "league" ("tier");`);
    this.addSql(`create index "idx_leagues_parent" on "league" ("parent_league_id");`);
    this.addSql(
      `alter table "league" add constraint "leagues_gameworld_id_id_key" unique ("gameworld_id", "id");`
    );

    this.addSql(
      `create table "league_children" ("parent_league_id" uuid not null, "child_league_id" uuid not null, constraint "league_children_pkey" primary key ("parent_league_id", "child_league_id"));`
    );

    this.addSql(
      `create table "league_rules" ("league_id" uuid not null, "promotion_spots" int not null, "relegation_spots" int not null, "team_count" int not null, constraint "league_rules_pkey" primary key ("league_id"));`
    );

    this.addSql(
      `create table "team" ("team_id" uuid not null, "gameworld_id" uuid not null, "league_id" uuid not null, "tier" int not null, "team_name" varchar(100) not null, "manager_id" uuid null, "balance" int not null default 300000, "played" int not null default 0, "points" int not null default 0, "goals_for" int not null default 0, "goals_against" int not null default 0, "wins" int not null default 0, "draws" int not null default 0, "losses" int not null default 0, "selection_order" text[] not null default '{}', constraint "team_pkey" primary key ("team_id"));`
    );
    this.addSql(`create index "idx_teams_league" on "team" ("league_id");`);
    this.addSql(
      `alter table "team" add constraint "teams_gameworld_id_team_id_key" unique ("gameworld_id", "team_id");`
    );

    this.addSql(
      `create table "players" ("player_id" uuid not null, "gameworld_id" uuid not null, "team_id" uuid not null, "league_id" uuid not null, "age" int not null, "seed" int not null, "first_name" varchar(255) not null, "surname" varchar(255) not null, "value" int not null, constraint "players_pkey" primary key ("player_id"));`
    );
    this.addSql(
      `alter table "players" add constraint "players_gameworld_id_player_id_key" unique ("gameworld_id", "player_id");`
    );

    this.addSql(
      `create table "player_overall_stats" ("player_id" uuid not null, "yellow_cards" int not null default 0, "red_cards" int not null default 0, "passes_completed" int not null default 0, "passes_attempted" int not null default 0, "successful_ball_carries" int not null default 0, "ball_carries_attempted" int not null default 0, "shots" int not null default 0, "shots_on_target" int not null default 0, "goals" int not null default 0, "saves" int not null default 0, "tackles" int not null default 0, "fouls" int not null default 0, constraint "player_overall_stats_pkey" primary key ("player_id"));`
    );

    this.addSql(
      `create table "player_match_history" ("player_id" uuid not null, "fixture_id" uuid not null, "yellow_cards" int not null default 0, "red_cards" int not null default 0, "passes_completed" int not null default 0, "passes_attempted" int not null default 0, "successful_ball_carries" int not null default 0, "ball_carries_attempted" int not null default 0, "shots" int not null default 0, "shots_on_target" int not null default 0, "goals" int not null default 0, "saves" int not null default 0, "tackles" int not null default 0, "fouls" int not null default 0, constraint "player_match_history_pkey" primary key ("player_id", "fixture_id"));`
    );

    this.addSql(
      `create table "player_attributes" ("player_id" uuid not null, "reflexes_current" int not null, "reflexes_potential" int not null, "positioning_current" int not null, "positioning_potential" int not null, "shot_stopping_current" int not null, "shot_stopping_potential" int not null, "tackling_current" int not null, "tackling_potential" int not null, "marking_current" int not null, "marking_potential" int not null, "heading_current" int not null, "heading_potential" int not null, "finishing_current" int not null, "finishing_potential" int not null, "pace_current" int not null, "pace_potential" int not null, "crossing_current" int not null, "crossing_potential" int not null, "passing_current" int not null, "passing_potential" int not null, "vision_current" int not null, "vision_potential" int not null, "ball_control_current" int not null, "ball_control_potential" int not null, constraint "player_attributes_pkey" primary key ("player_id"));`
    );

    this.addSql(
      `alter table "league" add constraint "league_parent_league_id_foreign" foreign key ("parent_league_id") references "league" ("id") on update cascade on delete set null;`
    );

    this.addSql(
      `alter table "league_children" add constraint "league_children_parent_league_id_foreign" foreign key ("parent_league_id") references "league" ("id") on update cascade on delete cascade;`
    );
    this.addSql(
      `alter table "league_children" add constraint "league_children_child_league_id_foreign" foreign key ("child_league_id") references "league" ("id") on update cascade on delete cascade;`
    );

    this.addSql(
      `alter table "league_rules" add constraint "league_rules_league_id_foreign" foreign key ("league_id") references "league" ("id") on update cascade on delete cascade;`
    );

    this.addSql(
      `alter table "team" add constraint "team_league_id_foreign" foreign key ("league_id") references "league" ("id") on update cascade;`
    );

    this.addSql(
      `alter table "players" add constraint "players_team_id_foreign" foreign key ("team_id") references "team" ("team_id") on update cascade;`
    );

    this.addSql(
      `alter table "player_overall_stats" add constraint "player_overall_stats_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on update cascade on delete cascade;`
    );

    this.addSql(
      `alter table "player_match_history" add constraint "player_match_history_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on update cascade;`
    );

    this.addSql(
      `alter table "player_attributes" add constraint "player_attributes_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on update cascade on delete cascade;`
    );
  }
}

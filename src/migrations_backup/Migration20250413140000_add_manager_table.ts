import { Migration } from '@mikro-orm/migrations';

export class Migration20250413140000_add_manager_table extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `create table "manager" ("manager_id" uuid not null, "created_at" bigint not null, "last_active" bigint not null, "first_name" varchar(100) null, "last_name" varchar(100) null, "team_id" uuid null, "gameworld_id" uuid null, "scout_tokens" int not null default 0, "super_scout_tokens" int not null default 0, constraint "manager_pkey" primary key ("manager_id"));`
    );
    this.addSql(`alter table "manager" add constraint "manager_id_key" unique ("manager_id");`);

    this.addSql(
      `alter table "manager" add constraint "manager_team_id_foreign" foreign key ("team_id") references "team" ("team_id") on update cascade on delete set null;`
    );
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "manager" cascade;`);
  }
}

import { Migration } from '@mikro-orm/migrations';

export class Migration20250418101724 extends Migration {
  override async up(): Promise<void> {
    this.addSql(`alter table "team" alter column "selection_order" drop default;`);
    this.addSql(
      `alter table "team" alter column "selection_order" type text[] using ("selection_order"::text[]);`
    );
  }

  override async down(): Promise<void> {
    this.addSql(
      `alter table "team" alter column "selection_order" type text[] using ("selection_order"::text[]);`
    );
    this.addSql(`alter table "team" alter column "selection_order" set default '{}';`);
  }
}

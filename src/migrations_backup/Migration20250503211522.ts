import { Migration } from '@mikro-orm/migrations';

export class Migration20250503211522 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `alter table "players" alter column "last_match_played" type bigint using ("last_match_played"::bigint);`
    );
  }

  override async down(): Promise<void> {
    this.addSql(
      `alter table "players" alter column "last_match_played" type int using ("last_match_played"::int);`
    );
  }
}

import { Migration } from '@mikro-orm/migrations';

export class Migration20250504080138 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `create table "season_end" ("id" uuid not null default uuid_generate_v4(), "gameworld_id" uuid not null, "end_date" bigint not null, constraint "season_end_pkey" primary key ("id"));`
    );
    this.addSql(`alter table "season_end" add constraint "season_end_id_unique" unique ("id");`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "season_end" cascade;`);
  }
}

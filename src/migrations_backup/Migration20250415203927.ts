import { Migration } from '@mikro-orm/migrations';

export class Migration20250415203927 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `alter table "manager" add constraint "manager_team_id_unique" unique ("team_id");`
    );

    this.addSql(`alter table "fixture" alter column "seed" type bigint using ("seed"::bigint);`);

    this.addSql(
      `alter table "player_match_history" add column "fixture_fixture_id" uuid not null;`
    );
    this.addSql(
      `alter table "player_match_history" add constraint "player_match_history_fixture_fixture_id_foreign" foreign key ("fixture_fixture_id") references "fixture" ("fixture_id") on update cascade;`
    );
    this.addSql(
      `alter table "player_match_history" add constraint "player_match_history_fixture_fixture_id_unique" unique ("fixture_fixture_id");`
    );
  }

  override async down(): Promise<void> {
    this.addSql(
      `alter table "player_match_history" drop constraint "player_match_history_fixture_fixture_id_foreign";`
    );

    this.addSql(
      `alter table "player_match_history" drop constraint "player_match_history_fixture_fixture_id_unique";`
    );
    this.addSql(`alter table "player_match_history" drop column "fixture_fixture_id";`);

    this.addSql(`alter table "manager" drop constraint "manager_team_id_unique";`);

    this.addSql(`alter table "fixture" alter column "seed" type int using ("seed"::int);`);
  }
}

import { Migration } from '@mikro-orm/migrations';

export class Migration20250415215209 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `create table "scouted_players" ("team_team_id" uuid not null, "player_player_id" uuid not null, "gameworld_id" uuid not null, "scouted_at" bigint not null, constraint "scouted_players_pkey" primary key ("team_team_id", "player_player_id"));`
    );
    this.addSql(
      `alter table "scouted_players" add constraint "scouted_players_gameworld_id_team_team_id_player__1708a_unique" unique ("gameworld_id", "team_team_id", "player_player_id");`
    );

    this.addSql(
      `alter table "scouted_players" add constraint "scouted_players_team_team_id_foreign" foreign key ("team_team_id") references "team" ("team_id") on update cascade;`
    );
    this.addSql(
      `alter table "scouted_players" add constraint "scouted_players_player_player_id_foreign" foreign key ("player_player_id") references "players" ("player_id") on update cascade;`
    );
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "scouted_players" cascade;`);
  }
}

import { Migration } from '@mikro-orm/migrations';

export class Migration20250506122616 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `alter table "player_attributes" alter column "stamina" type real using ("stamina"::real);`
    );
  }

  override async down(): Promise<void> {
    this.addSql(
      `alter table "player_attributes" alter column "stamina" type int using ("stamina"::int);`
    );
  }
}

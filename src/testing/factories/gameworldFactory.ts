import { Gameworld } from '@/entities/Gameworld.js';
import { League } from '@/entities/League.js';
import { Collection } from '@mikro-orm/core';
import { Factory } from 'interface-forge';

export const GameworldFactory = new Factory<Gameworld>((factory: Factory<Gameworld>) => {
  return {
    id: factory.string.uuid(),
    endDate: factory.date.future().getTime(),
    leagues: new Collection<League>({}),
  };
});

import { Manager, NotificationCategory, NotificationChannel } from '@/entities/Manager.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import { Factory } from 'interface-forge';

export const ManagerFactory = new Factory<Manager>(
  (factory: Factory<Manager>, iteration: number) => {
    return {
      managerId: factory.string.uuid(),
      createdAt: factory.date.past().getTime(),
      lastActive: factory.date.past().getTime(),
      firstName: factory.person.firstName(),
      lastName: factory.person.lastName(),
      email: factory.internet.email(),
      team: TeamsFactory.build(),
      gameworldId: factory.string.uuid(),
      scoutTokens: factory.number.int({ min: 0, max: 10 }),
      superScoutTokens: factory.number.int({ min: 0, max: 10 }),
      magicSponges: factory.number.int({ min: 0, max: 10 }),
      cardAppeals: factory.number.int({ min: 0, max: 10 }),
      trainingBoosts: factory.number.int({ min: 0, max: 10 }),
      notificationPreferences: {
        [NotificationCategory.TRANSFERS]: {
          [NotificationChannel.PUSH]: factory.datatype.boolean(),
          [NotificationChannel.EMAIL]: factory.datatype.boolean(),
        },
        [NotificationCategory.TRAINING]: {
          [NotificationChannel.PUSH]: factory.datatype.boolean(),
          [NotificationChannel.EMAIL]: factory.datatype.boolean(),
        },
        [NotificationCategory.PRE_MATCH]: {
          [NotificationChannel.PUSH]: factory.datatype.boolean(),
          [NotificationChannel.EMAIL]: factory.datatype.boolean(),
        },
        [NotificationCategory.SCOUTING_RESULTS]: {
          [NotificationChannel.PUSH]: factory.datatype.boolean(),
          [NotificationChannel.EMAIL]: factory.datatype.boolean(),
        },
        [NotificationCategory.POST_MATCH]: {
          [NotificationChannel.PUSH]: factory.datatype.boolean(),
          [NotificationChannel.EMAIL]: factory.datatype.boolean(),
        },
        [NotificationCategory.SYSTEM_ANNOUNCEMENTS]: {
          [NotificationChannel.PUSH]: factory.datatype.boolean(),
          [NotificationChannel.EMAIL]: factory.datatype.boolean(),
        },
      },
      pushToken: factory.string.uuid(),
      loginStreak: factory.number.int({ min: 0, max: 10 }),
      lastLogin: factory.date.past().getTime(),
    };
  }
);

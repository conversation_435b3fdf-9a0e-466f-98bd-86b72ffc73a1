import { MatchEngine } from '@/simulation/match-engine.js';
import { GamePlayer, Team as GameTeam } from '@/simulation/types.js';
import { PlayerFactory } from '@/testing/factories/playerFactory.js';
import { logger } from '@/utils/logger.js';
import { setRandomSeed } from '@/utils/seeded-random.js';
import fs from 'fs';
import path from 'path';

// Redirect logger output to a file
const logFilePath = path.join(process.cwd(), 'match-simulation.log');
const logStream = fs.createWriteStream(logFilePath, { flags: 'w' });

// Override logger to write to file
// eslint-disable-next-line @typescript-eslint/unbound-method
const originalLocalLog = logger.local;
logger.local = (message) => {
  logStream.write(message + '\n');
  originalLocalLog(message); // Keep console output if desired
  return message;
};

// Create mock players with initial energy values
function createMockTeam(teamName: string, teamId: string, numPlayers = 16): GameTeam {
  const players: GamePlayer[] = [];

  // Create the required number of players
  for (let i = 0; i < numPlayers; i++) {
    const mockPlayer = PlayerFactory.build();

    // Set energy levels - starters have higher energy than subs
    mockPlayer.energy = i < 11 ? 85 : 100;

    // Customize positions - adjust attributes based on position
    if (i === 0) {
      // Goalkeeper
      mockPlayer.attributes.reflexesCurrent = 18;
      mockPlayer.attributes.positioningCurrent = 17;
    } else if (i >= 1 && i <= 4) {
      // Defenders
      mockPlayer.attributes.tacklingCurrent = 16;
      mockPlayer.attributes.markingCurrent = 15;
    } else if (i >= 5 && i <= 8) {
      // Midfielders
      mockPlayer.attributes.passingCurrent = 15;
      mockPlayer.attributes.ballControlCurrent = 14;
    } else if (i >= 9 && i <= 10) {
      // Forwards
      mockPlayer.attributes.finishingCurrent = 16;
      mockPlayer.attributes.paceCurrent = 15;
    }

    // Create game player with required properties
    players.push({
      player: mockPlayer,
      stats: {
        shotsOnTarget: 0,
        goals: 0,
        saves: 0,
        tackles: 0,
        fouls: 0,
        yellowCards: 0,
        redCards: 0,
        shots: 0,
        passesAttempted: 0,
        passesCompleted: 0,
        ballCarriesAttempted: 0,
        successfulBallCarries: 0,
      },
      hasBeenSubbed: false,
      isInjured: false,
    });
  }

  return {
    gameworldId: '',
    standings: {
      played: 0,
      points: 0,
      goalsFor: 0,
      goalsAgainst: 0,
      wins: 0,
      draws: 0,
      losses: 0,
    },
    teamId,
    teamName,
    players,
  };
}

// Create mock teams
const homeTeam = createMockTeam('Home FC', 'home-team-id');
const awayTeam = createMockTeam('Away United', 'away-team-id');

// Set up some test scenarios
// 1. One player with very low energy who should get subbed
homeTeam.players[5]!.player.energy = 35;

console.log('Starting match simulation...');

// set the seed
setRandomSeed(1);

// Create match engine and run simulation
const matchEngine = new MatchEngine(homeTeam, awayTeam);
const result = matchEngine.simulate();

// Log results summary
logStream.write('\n\n--- MATCH SUMMARY ---\n');
logStream.write(`Final Score: Home ${result.stats.score[0]} - ${result.stats.score[1]} Away\n`);
logStream.write(
  `Possession: Home ${Math.round((result.stats.possession[0] / (result.stats.possession[0] + result.stats.possession[1])) * 100)}% - ${Math.round((result.stats.possession[1] / (result.stats.possession[0] + result.stats.possession[1])) * 100)}% Away\n`
);
logStream.write(`Red Cards: Home ${result.stats.redCards[0]} - ${result.stats.redCards[1]} Away\n`);
logStream.write(
  `Substitutions: Home ${homeTeam.players.filter((p) => p.hasBeenSubbed).length / 2} - ${awayTeam.players.filter((p) => p.hasBeenSubbed).length / 2} Away\n`
);

logStream.end();

console.log(`Match simulation complete! Results written to ${logFilePath}`);

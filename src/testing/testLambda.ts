/* eslint-disable @typescript-eslint/no-unsafe-argument,@typescript-eslint/no-explicit-any */
import { handler } from '@/functions/training/scheduleTrainingImprovement.js';
import { initializeDatabase } from '@/storage-interface/database-initializer.js';
import createSqsEvent from '@/testing/createSqsEvent.js';
import { logger } from '@/utils/logger.js';

process.env.DATABASE_TYPE = 'postgres';
process.env.DEBUG_USER_ID = '118bf5a0-7011-70ac-b498-efb221ba66be';

async function startTest() {
  await initializeDatabase();

  /*const mockEvent = createHttpEvent({
    pathParameters: {
      gameworldId: '3aa91af4-83e7-40e2-9297-203288058b64',
      teamId: 'f705898b-6d70-4fc3-8054-74256c3d213f',
    },
    httpMethod: 'GET',
  });*/
  const mockEvent = createSqsEvent([
    {
      messageId: '1315fb50-513c-4d54-b689-e9e9eb42bb0c',
      receiptHandle:
        'AQEBn1ievFX/50zV7HfsMqgoxpjgQdPx+nosgAwbVmPJCGaSUbcaWTBOuLnrLiF2xwc9r0TJRle3npCv1bN8YAgxmO/QiwzjGLETtqjL5ODtKagV2TZ+KTZ0iuw+nAmIWLTDF3BVvQWvnwNS3P5G5qcB9C8kFoWJzyofqi/NBeR0sJhI//HphYpWz6wepeV11+GemB+sdRiGQkywqcnhRABm3Gi+RvBSEll1SY5MZPXzJUSvIiIm4nsdqVdtOraGA420p7CqVV9KfQqL7GSq0kVJ9eu7VFRDSckEeWiqPxUVODcuthzbgPA+TsdFHAaISba8AWiZc7hTQR5v3uKYlP8KKb8hdodGlshM3FU3ohQKDOkAlRMs6GaYEt+cd9BMmZlYc3S8H6gB/0JvWA8Qi03UXj63R8YVsC/gNsiqDjzZKi8=',
      body: {
        playerId: '10652acc-937a-403b-aad4-24bb71ee6c9c',
        playerName: 'Nathaniel Clayton',
        attribute: 'passing',
        current: 17,
        potential: 21,
      },
      attributes: {
        ApproximateReceiveCount: '1',
        AWSTraceHeader:
          'Root=1-6840de12-2233cfbf683012374e85710c;Parent=c55e8802b2a13464;Sampled=1;Lineage=1:a38b5de3:0',
        SentTimestamp: '1749081627340',
        SenderId: 'AROA5CZHT4CSDANSC7V63:stage-scheduleTrainingImprovement-8c7e872',
        ApproximateFirstReceiveTimestamp: '1749081627347',
      },
      messageAttributes: {},
      md5OfMessageAttributes: null,
      md5OfBody: '5dffa8c019c5dd1a687f9b3df21ccc5f',
      eventSource: 'aws:sqs',
      eventSourceARN: 'arn:aws:sqs:us-east-2:899341869220:stage-trainingQueue-920e959',
      awsRegion: 'us-east-2',
    },
    {
      messageId: '545018a5-6de5-419f-a262-ba4dc618211c',
      receiptHandle:
        'AQEBnLnPrv2N5ccVr2GiCjz0Tlft2jwZpAfvSvELrkMJJN+vuCbCpRCa6Cr39I/uAyD5YZ07eozcJTrOY3d4anWIYrjnxAJhEpeqscuUjvppQhgexozIkwQ7/oFbnhhMH72Fs+n3bBrK+zf/fF7qGHp3JGVxm0Vz/QZdkZplPB2DZskT9klN4beZig+8+3vSd7ytSLQIlNDPZrpD5+wIGIcu5pFJkNgwEOje+1q22UQvqiXJJceujjy8kYlwhFK2UAju8qEwJRwty2jxAxy+0iZoQmP6mdVXn9QodAVIBHbH3Bs5qpxcF0rajqjsXq8T813KwC8Uy3qux6p+20J9Bto7ajGE3BO0hedANOrSDbvWDKoFCJ1QrFJGOdFa7GYEkz62Zx6hvJ+4i+vuyGfCSPv/gxGu7trIPE7pCzSYCEnLcfo=',
      body: {
        playerId: '7282c7c1-5f9c-400d-bfdf-84c78d180909',
        playerName: 'Thomas Watkins',
        attribute: 'positioning',
        current: 8,
        potential: 15,
      },
      attributes: {
        ApproximateReceiveCount: '1',
        AWSTraceHeader:
          'Root=1-6840de12-2233cfbf683012374e85710c;Parent=c55e8802b2a13464;Sampled=1;Lineage=1:a38b5de3:0',
        SentTimestamp: '1749081627347',
        SenderId: 'AROA5CZHT4CSDANSC7V63:stage-scheduleTrainingImprovement-8c7e872',
        ApproximateFirstReceiveTimestamp: '1749081627348',
      },
      messageAttributes: {},
      md5OfMessageAttributes: null,
      md5OfBody: 'c629009d669675de9943dd619c642454',
      eventSource: 'aws:sqs',
      eventSourceARN: 'arn:aws:sqs:us-east-2:899341869220:stage-trainingQueue-920e959',
      awsRegion: 'us-east-2',
    },
    {
      messageId: 'a2161bac-60c7-4c1a-b53e-b37249706682',
      receiptHandle:
        'AQEBPRSCkpSLUelSScT8yOBRj+bDbCxIEywTq2sS9s4PKIY7Qyhi+pwXMCcRrl/YzrTK3GjC/iAmIG5Ssz9TPad0ezAMhPIiCxvQl6NDA4/y9dnMw4/FkUeF9+vpu1ygPKy7pwQhfxo9QC3YMxHLgm/MrkzlTjetQpLsWXJuwORfpBzoXBb/KQJrcsXGmININB+YZ09/GhuWQPgsnSBzogvfCGZuii+9khRu6wYs2hmnIEILr5EWio3uRb7kNK4cTiNRoMq2pX7+5Gt0cLI4GvkhrYQKB/u5GV+WlivZKfO0ujtCOMeS2Z3FJzWrwU0pRSPD34z0NfnzoPrD/9yIzMOVLBAy/lLL5ReKiDwlYyY0bZAss7xirEOQP30zf/y74RF+wLTCnbPFBi9wE10ynilzc7KgEf6Fq6uTeJAwL5+mX1c=',
      body: {
        playerId: '7282c7c1-5f9c-400d-bfdf-84c78d180909',
        playerName: 'Thomas Watkins',
        attribute: 'tackling',
        current: 17,
        potential: 24,
      },
      attributes: {
        ApproximateReceiveCount: '1',
        AWSTraceHeader:
          'Root=1-6840de12-2233cfbf683012374e85710c;Parent=c55e8802b2a13464;Sampled=1;Lineage=1:a38b5de3:0',
        SentTimestamp: '1749081627340',
        SenderId: 'AROA5CZHT4CSDANSC7V63:stage-scheduleTrainingImprovement-8c7e872',
        ApproximateFirstReceiveTimestamp: '1749081627347',
      },
      messageAttributes: {},
      md5OfMessageAttributes: null,
      md5OfBody: '513dc0b7688e8fdd58e24e25d0a5a0e2',
      eventSource: 'aws:sqs',
      eventSourceARN: 'arn:aws:sqs:us-east-2:899341869220:stage-trainingQueue-920e959',
      awsRegion: 'us-east-2',
    },
  ]) as any;
  /*const result = await (await getTransferRepository()).getCompletedAuctions();
  logger.debug('result', { result });
  return;*/
  const response = await handler(mockEvent, {} as any);
  logger.debug('response', { response });
}

startTest();

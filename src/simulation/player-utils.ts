import { Player } from '@/entities/Player.js';
import { CurrentAttributes } from '@/model/player.js';
import { GamePlayer } from '@/simulation/types.js';

export function sortPlayersByPosition(players: Player[]): GamePlayer[] {
  const calculateScore = (player: Player, attributes: (keyof CurrentAttributes)[]): number => {
    return (
      attributes.reduce((sum, attr) => {
        // Use a type assertion to safely access the dynamic property
        const key = `${attr}Current` as keyof typeof player.attributes;
        return sum + (player.attributes![key] as number);
      }, 0) / attributes.length
    );
  };

  // Create a copy of players to avoid mutating the original array
  const sortedPlayers = [...players];

  // Define attribute groups for each position
  const positionAttributes = {
    goalkeeper: ['reflexes', 'positioning', 'shotStopping'] as (keyof CurrentAttributes)[],
    defender: ['tackling', 'marking', 'heading'] as (keyof CurrentAttributes)[],
    midfielder: ['passing', 'vision', 'ballControl'] as (keyof CurrentAttributes)[],
    attacker: ['finishing', 'pace', 'crossing'] as (keyof CurrentAttributes)[],
  };

  // Calculate scores for each player in each position
  const playerScores = sortedPlayers.map((player) => ({
    player,
    scores: {
      goalkeeper: calculateScore(player, positionAttributes.goalkeeper),
      defender: calculateScore(player, positionAttributes.defender),
      midfielder: calculateScore(player, positionAttributes.midfielder),
      attacker: calculateScore(player, positionAttributes.attacker),
    },
  }));

  // Sort players into positions
  const result: Player[] = new Array(players.length) as Player[];

  // Find goalkeeper (index 0)
  playerScores.sort((a, b) => b.scores.goalkeeper - a.scores.goalkeeper);
  result[0] = playerScores[0]!.player;
  playerScores.shift();

  // Find defenders (indices 1-4)
  playerScores.sort((a, b) => b.scores.defender - a.scores.defender);
  for (let i = 1; i <= 4; i++) {
    result[i] = playerScores[0]!.player;
    playerScores.shift();
  }

  // Find midfielders (indices 5-8)
  playerScores.sort((a, b) => b.scores.midfielder - a.scores.midfielder);
  for (let i = 5; i <= 8; i++) {
    result[i] = playerScores[0]!.player;
    playerScores.shift();
  }

  // Find attackers (indices 9-10)
  playerScores.sort((a, b) => b.scores.attacker - a.scores.attacker);
  for (let i = 9; i <= 10; i++) {
    result[i] = playerScores[0]!.player;
    playerScores.shift();
  }

  // Add remaining players as substitutes, sorted by their best position score
  if (playerScores.length > 0) {
    playerScores.forEach((playerScore, index) => {
      result[11 + index] = playerScore.player;
    });
  }

  return result.map((player) => ({
    player: player,
    stats: {
      yellowCards: 0,
      redCards: 0,
      passesCompleted: 0,
      passesAttempted: 0,
      successfulBallCarries: 0,
      ballCarriesAttempted: 0,
      shots: 0,
      shotsOnTarget: 0,
      goals: 0,
      saves: 0,
      tackles: 0,
      fouls: 0,
    },
    isInjured: false,
    hasBeenSubbed: false,
  }));
}

export function getAttributeValue(player: Player, attribute: keyof CurrentAttributes): number {
  const key = `${attribute}Current` as keyof typeof player.attributes;
  return player.attributes![key] as number;
}

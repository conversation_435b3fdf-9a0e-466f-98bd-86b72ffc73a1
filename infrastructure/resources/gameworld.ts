import { Table } from '@pulumi/aws/dynamodb';

import { createLambdaErrorLogAlarm } from '../cloudwatch';
import { sqsBatchWindow, stageName } from '../config';
import { addDynamoPolicyToRole } from '../dynamodb';
import { createScheduledRule } from '../eventBridge';
import { addLambdaInvokePolicyToRole, createLambdaFunction } from '../lambda';
import {
  addLambdaPermissionForSQS,
  addQueueReadPolicyToRole,
  addQueueSendPolicyToRole,
  createDLQ,
  createQueue,
} from '../queue';
import { createCloudWatchAlarmTopic, createMonitoredEventSourceMapping } from '../queueMonitoring';

export interface GameworldResourcesConfig {
  leaguesTable: Table;
  teamsTable: Table;
}

export function createGameworldResources(config: GameworldResourcesConfig) {
  // Gameworld creation handler
  let gameworldRole = addDynamoPolicyToRole(
    'gameworldHandler',
    [config.leaguesTable],
    ['dynamodb:PutItem', 'dynamodb:BatchWriteItem']
  );

  // Create unattached players queue for direct access
  const unattachedPlayersDLQ = createDLQ('gameworldUnattachedPlayersQueue');
  const unattachedPlayersQueue = createQueue(
    'gameworldUnattachedPlayersQueue',
    unattachedPlayersDLQ
  );

  const generatePlayersDLQ = createDLQ('generatePlayersQueue');
  const generatePlayersQueue = createQueue('generatePlayersQueue', generatePlayersDLQ);

  // Create team queue for direct access
  const teamDLQ = createDLQ('gameworldTeamQueue');
  const teamQueue = createQueue('gameworldTeamQueue', teamDLQ);

  // Add send permissions to the gameworld role for both queues
  gameworldRole = addQueueSendPolicyToRole(
    'gameworldToUnattachedPlayersQueue',
    unattachedPlayersQueue,
    gameworldRole
  );
  gameworldRole = addQueueSendPolicyToRole('gameworldToTeamQueue', teamQueue, gameworldRole);

  const [gameworldLambda] = createLambdaFunction(
    'gameworldHandler',
    '../dist/generate/gameworld',
    'index.handler',
    {
      LEAGUES_TABLE_NAME: config.leaguesTable.name,
      UNATTACHED_PLAYERS_QUEUE_URL: unattachedPlayersQueue.url,
      TEAM_QUEUE_URL: teamQueue.url,
    },
    gameworldRole
  );

  const [generateFixturesLambda] = createLambdaFunction(
    'generateFixtures',
    '../dist/generate/generateFixtures',
    'index.handler'
  );

  // End of season processing
  const endOfSeasonDLQ = createDLQ('endOfSeasonQueue');
  const endOfSeasonQueue = createQueue('endOfSeasonQueue', endOfSeasonDLQ, 1);

  const endOfSeasonQueueRole = addQueueReadPolicyToRole('endOfSeasonQueue', endOfSeasonQueue);

  let endOfSeasonRole = addDynamoPolicyToRole(
    'processEndOfSeason',
    [config.leaguesTable, config.teamsTable],
    ['dynamodb:Query', 'dynamodb:UpdateItem'],
    endOfSeasonQueueRole
  );

  // Add permission for team lambda to invoke generate fixtures lambda
  endOfSeasonRole = addLambdaInvokePolicyToRole(
    'processEndOfSeasonInvokeGenerateFixtures',
    [generateFixturesLambda],
    endOfSeasonRole
  );

  const [endOfSeasonLambda, endOfSeasonLogGroup] = createLambdaFunction(
    'processEndOfSeason',
    '../dist/league/processEndOfSeason',
    'index.handler',
    {
      QUEUE_URL: endOfSeasonQueue.url,
      TEAMS_TABLE_NAME: config.teamsTable.name,
      LEAGUES_TABLE_NAME: config.leaguesTable.name,
      GENERATE_FIXTURES_LAMBDA_ARN: generateFixturesLambda.arn,
      GENERATE_PLAYERS_QUEUE_URL: generatePlayersQueue.url,
    },
    endOfSeasonRole,
    undefined,
    {
      memorySize: 256,
      timeout: 60,
    }
  );

  // Add Lambda Permission for SQS to invoke it
  addLambdaPermissionForSQS('processEndOfSeason', endOfSeasonLambda, endOfSeasonQueue);

  // Create an SNS topic for CloudWatch alarms
  const errorAlarmTopic = createCloudWatchAlarmTopic('gameworld-error');

  // Add SQS Queue Event Source Mapping to Lambda with monitoring
  createMonitoredEventSourceMapping(
    'processEndOfSeason',
    endOfSeasonLambda,
    endOfSeasonLogGroup,
    endOfSeasonQueue,
    10, // batchSize
    sqsBatchWindow, // maximumBatchingWindowInSeconds
    undefined, // additionalConfig
    [errorAlarmTopic.arn] // alarmActions
  );

  const checkRole = addQueueSendPolicyToRole('checkForSeasonEnd', endOfSeasonQueue);
  const [checkForSeasonEnd] = createLambdaFunction(
    'checkForSeasonEnd',
    '../dist/gameworld/checkForSeasonEnd',
    'index.handler',
    {
      QUEUE_URL: endOfSeasonQueue.url,
    },
    checkRole
  );

  // Create EventBridge rules to trigger the lambda at midnight UK time
  createScheduledRule({
    name: 'check-season-end',
    description: 'Triggers a lambda to see if any gameworlds season has ended',
    scheduleExpression: 'cron(0 0 * * ? *)', // Midnight daily
    lambda: checkForSeasonEnd,
  });

  const generatePlayersQueueRole = addQueueReadPolicyToRole(
    'generatePlayersQueue',
    generatePlayersQueue
  );

  const [generatePlayersLambda, generatePlayersLambdaLogGroup] = createLambdaFunction(
    'generatePlayersHandler',
    '../dist/generate/generate-players',
    'index.handler',
    {
      QUEUE_URL: generatePlayersQueue.url,
    },
    generatePlayersQueueRole
  );

  if (stageName !== 'dev') {
    createLambdaErrorLogAlarm(
      generatePlayersLambda,
      {
        name: 'generatePlayersHandler',
        description: 'Alarm for error logs in generatePlayersHandler Lambda function',
        alarmActions: [errorAlarmTopic.arn],
      },
      generatePlayersLambdaLogGroup
    );
  }

  return {
    gameworldLambda,
    endOfSeasonLambda,
    endOfSeasonQueue,
    teamQueue,
    unattachedPlayersQueue,
    errorAlarmTopic,
    generateFixturesLambda,
    generatePlayersQueue,
  };
}

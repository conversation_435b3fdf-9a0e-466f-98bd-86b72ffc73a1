import { createLambdaErrorLogAlarm } from '../cloudwatch';
import { stageName } from '../config';
import { createScheduledRule } from '../eventBridge';
import { createLambdaFunction } from '../lambda';
import {
  addLambdaPermissionForSQS,
  addQueueReadPolicyToRole,
  addQueueSendPolicyToRole,
  createDLQ,
  createEventSourceMapping,
  createQueue,
} from '../queue';
import { createCloudWatchAlarmTopic } from '../queueMonitoring';

interface TrainingResourcesConfig {}

export function createTrainingResources(config: TrainingResourcesConfig) {
  const [getTrainingSlotsLambda] = createLambdaFunction(
    'getTrainingSlots',
    '../dist/training/getTrainingSlots',
    'index.handler'
  );

  const [assignTrainingSlotLambda] = createLambdaFunction(
    'assignTrainingSlot',
    '../dist/training/assignTrainingSlot',
    'index.handler'
  );

  const [unlockTrainingSlotLambda] = createLambdaFunction(
    'unlockTrainingSlot',
    '../dist/training/unlockTrainingSlot',
    'index.handler'
  );

  // Create SQS Queues and DLQs for each type
  const trainingDLQ = createDLQ('trainingQueue');
  const trainingQueue = createQueue('trainingQueue', trainingDLQ, 3, {
    visibilityTimeout: 300,
  });

  let writeRole = addQueueSendPolicyToRole('scheduleTrainingImprovement', trainingQueue);
  const [scheduleTrainingImprovementLambda, scheduleTrainingImprovementLogGroup] =
    createLambdaFunction(
      'scheduleTrainingImprovement',
      '../dist/training/scheduleTrainingImprovement',
      'index.handler',
      {
        TRAINING_QUEUE_URL: trainingQueue.url,
      },
      writeRole
    );

  // Schedule to run at 12am and 12pm
  createScheduledRule({
    name: 'schedule-training-improvement-12pm',
    description: 'Trigger training improvement at 12pm every day',
    scheduleExpression: 'cron(0 12 * * ? *)',
    lambda: scheduleTrainingImprovementLambda,
  });

  createScheduledRule({
    name: 'schedule-training-improvement-8am',
    description: 'Trigger training improvement at 12am every day',
    scheduleExpression: 'cron(0 0 * * ? *)',
    lambda: scheduleTrainingImprovementLambda,
  });

  let readRole = addQueueReadPolicyToRole('processTrainingImprovement', trainingQueue);
  const [processTrainingImprovementLambda, processTrainingImprovementLogGroup] =
    createLambdaFunction(
      'processTrainingImprovement',
      '../dist/training/processTrainingImprovements',
      'index.handler',
      {
        TRAINING_QUEUE_URL: trainingQueue.url,
      },
      readRole
    );

  // Allow SQS to invoke the reader Lambda
  addLambdaPermissionForSQS(
    'processTrainingImprovement',
    processTrainingImprovementLambda,
    trainingQueue
  );

  // Event source mapping to trigger Lambda from queue
  createEventSourceMapping(
    'processTrainingImprovement',
    processTrainingImprovementLambda,
    trainingQueue,
    10, // batchSize
    60 // maximumBatchingWindowInSeconds
  );

  // Create an SNS topic for CloudWatch alarms
  const errorAlarmTopic = createCloudWatchAlarmTopic('training-error');

  if (stageName !== 'dev') {
    createLambdaErrorLogAlarm(
      processTrainingImprovementLambda,
      {
        name: 'processTrainingImprovement',
        description: 'Alarm for error logs in processTrainingImprovement Lambda function',
        alarmActions: [errorAlarmTopic.arn],
      },
      processTrainingImprovementLogGroup
    );
    createLambdaErrorLogAlarm(
      scheduleTrainingImprovementLambda,
      {
        name: 'scheduleTrainingImprovement',
        description: 'Alarm for error logs in scheduleTrainingImprovement Lambda function',
        alarmActions: [errorAlarmTopic.arn],
      },
      scheduleTrainingImprovementLogGroup
    );
  }

  return {
    getTrainingSlotsLambda,
    assignTrainingSlotLambda,
    unlockTrainingSlotLambda,
    errorAlarmTopic,
  };
}

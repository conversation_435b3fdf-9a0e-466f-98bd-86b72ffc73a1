import { Table } from '@pulumi/aws/dynamodb';
import { Function } from '@pulumi/aws/lambda';
import { Queue } from '@pulumi/aws/sqs';
import { sqsBatchWindow } from '../config';
import { addDynamoPolicyToRole } from '../dynamodb';
import { addLambdaInvokePolicyToRole, createLambdaFunction } from '../lambda';
import {
  addLambdaPermissionForSQS,
  addQueueReadPolicyToRole,
  addQueueSendPolicyToRole,
  createDLQ,
  createQueue,
} from '../queue';
import { createCloudWatchAlarmTopic, createMonitoredEventSourceMapping } from '../queueMonitoring';

export interface TeamResourcesConfig {
  managersTable: Table;
  playersTable: Table;
  teamsTable: Table;
  availableTeamsTable: Table;
  fixturesTable: Table;
  teamQueue: Queue;
  generateFixturesLambda: Function; // Lambda function for generating fixtures
}

export function createTeamResources(config: TeamResourcesConfig) {
  let teamQueueRole = addQueueReadPolicyToRole('teamQueue', config.teamQueue);

  // Create a player queue for direct access
  const playerDLQ = createDLQ('teamToPlayerQueue');
  const playerQueue = createQueue('teamToPlayerQueue', playerDLQ);

  // Add send permissions to the team role for the player queue
  teamQueueRole = addQueueSendPolicyToRole('teamToPlayerQueue', playerQueue, teamQueueRole);

  let generateTeamRole = addDynamoPolicyToRole(
    'generateTeamHandler',
    [config.teamsTable, config.availableTeamsTable, config.fixturesTable],
    ['dynamodb:PutItem', 'dynamodb:BatchWriteItem'],
    teamQueueRole
  );

  // Add permission for team lambda to invoke generate fixtures lambda
  generateTeamRole = addLambdaInvokePolicyToRole(
    'teamLambda',
    [config.generateFixturesLambda],
    generateTeamRole
  );

  // Create environment variables object
  const envVars: { [key: string]: any } = {
    QUEUE_URL: config.teamQueue.url,
    TEAMS_TABLE_NAME: config.teamsTable.name,
    AVAILABLE_TEAMS_TABLE_NAME: config.availableTeamsTable.name,
    FIXTURES_TABLE_NAME: config.fixturesTable.name,
    PLAYER_QUEUE_URL: playerQueue.url,
    GENERATE_FIXTURES_LAMBDA_ARN: config.generateFixturesLambda.arn,
  };

  const [generateTeamLambda, generateTeamLogGroup] = createLambdaFunction(
    'generateTeamHandler',
    '../dist/generate/team',
    'index.handler',
    envVars,
    generateTeamRole,
    undefined,
    {
      memorySize: 256,
      timeout: 60,
    }
  );

  // Add Lambda Permission for SQS to invoke it
  addLambdaPermissionForSQS('generateTeamHandler', generateTeamLambda, config.teamQueue);

  // Create an SNS topic for CloudWatch alarms
  const errorAlarmTopic = createCloudWatchAlarmTopic('team-error');

  // Tell the lambda to monitor the queue for new messages
  createMonitoredEventSourceMapping(
    'generateTeamHandler',
    generateTeamLambda,
    generateTeamLogGroup,
    config.teamQueue,
    10, // batchSize
    sqsBatchWindow, // maximumBatchingWindowInSeconds
    undefined, // additionalConfig
    [errorAlarmTopic.arn] // alarmActions
  );

  let getTeamRole = addDynamoPolicyToRole(
    'getTeamHandler',
    [config.teamsTable, config.playersTable],
    ['dynamodb:GetItem', 'dynamodb:Query']
  );
  const [getTeamLambda] = createLambdaFunction(
    'getTeamHandler',
    '../dist/team/getTeam',
    'index.handler',
    { TEAMS_TABLE_NAME: config.teamsTable.name, PLAYERS_TABLE_NAME: config.playersTable.name },
    getTeamRole
  );

  let updateTeamOrderRole = addDynamoPolicyToRole(
    'updateTeamOrderHandler',
    [config.teamsTable, config.managersTable],
    ['dynamodb:GetItem', 'dynamodb:UpdateItem']
  );
  const [updateTeamOrderLambda] = createLambdaFunction(
    'updateTeamOrderHandler',
    '../dist/team/updateTeamOrder',
    'index.handler',
    {
      TEAMS_TABLE_NAME: config.teamsTable.name,
      MANAGERS_TABLE_NAME: config.managersTable.name,
    },
    updateTeamOrderRole
  );

  const [upgradeTrainingLambda] = createLambdaFunction(
    'upgradeTrainingHandler',
    '../dist/team/upgradeTraining',
    'index.handler'
  );

  return {
    playerQueue,
    generateTeamLambda,
    getTeamLambda,
    updateTeamOrderLambda,
    upgradeTrainingLambda,
    errorAlarmTopic,
  };
}
